"""
Dependency Injection Container - Modern Architecture Pattern
==========================================================

Implements dependency injection for better modularity and testability:
- Service container with automatic dependency resolution
- Interface-based design with clear contracts
- Singleton and factory patterns
- Lifecycle management
- Configuration-driven service instantiation
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Type, TypeVar, Optional, Callable, List
from dataclasses import dataclass
from enum import Enum
import inspect
import asyncio

from .error_handling import ChimeraError


T = TypeVar('T')


class ServiceLifetime(Enum):
    """Service lifetime management"""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"


@dataclass
class ServiceDescriptor:
    """Service registration descriptor"""
    service_type: Type
    implementation_type: Optional[Type] = None
    factory: Optional[Callable] = None
    lifetime: ServiceLifetime = ServiceLifetime.SINGLETON
    dependencies: List[Type] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


class DependencyInjectionError(ChimeraError):
    """Dependency injection related error"""
    pass


class ServiceContainer:
    """
    Dependency injection container with automatic resolution
    """
    
    def __init__(self):
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._instances: Dict[Type, Any] = {}
        self._scoped_instances: Dict[str, Dict[Type, Any]] = {}
        self._building: set = set()  # Circular dependency detection
        
        logging.info("🏗️ Service container initialized")
    
    def register_singleton(self, service_type: Type[T], 
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None) -> 'ServiceContainer':
        """Register a singleton service"""
        return self._register_service(
            service_type, implementation_type, factory, ServiceLifetime.SINGLETON
        )
    
    def register_transient(self, service_type: Type[T],
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None) -> 'ServiceContainer':
        """Register a transient service (new instance each time)"""
        return self._register_service(
            service_type, implementation_type, factory, ServiceLifetime.TRANSIENT
        )
    
    def register_scoped(self, service_type: Type[T],
                       implementation_type: Optional[Type[T]] = None,
                       factory: Optional[Callable[[], T]] = None) -> 'ServiceContainer':
        """Register a scoped service (one instance per scope)"""
        return self._register_service(
            service_type, implementation_type, factory, ServiceLifetime.SCOPED
        )
    
    def _register_service(self, service_type: Type[T],
                         implementation_type: Optional[Type[T]],
                         factory: Optional[Callable[[], T]],
                         lifetime: ServiceLifetime) -> 'ServiceContainer':
        """Internal service registration"""
        if service_type in self._services:
            logging.warning(f"⚠️ Overriding existing service registration: {service_type.__name__}")
        
        # Determine implementation type
        impl_type = implementation_type or service_type
        
        # Extract dependencies from constructor
        dependencies = self._extract_dependencies(impl_type)
        
        descriptor = ServiceDescriptor(
            service_type=service_type,
            implementation_type=impl_type,
            factory=factory,
            lifetime=lifetime,
            dependencies=dependencies
        )
        
        self._services[service_type] = descriptor
        
        logging.debug(f"📝 Registered {lifetime.value} service: {service_type.__name__}")
        return self
    
    def _extract_dependencies(self, implementation_type: Type) -> List[Type]:
        """Extract dependencies from constructor signature"""
        try:
            signature = inspect.signature(implementation_type.__init__)
            dependencies = []
            
            for param_name, param in signature.parameters.items():
                if param_name == 'self':
                    continue
                
                if param.annotation != inspect.Parameter.empty:
                    dependencies.append(param.annotation)
            
            return dependencies
            
        except Exception as e:
            logging.warning(f"⚠️ Could not extract dependencies for {implementation_type.__name__}: {e}")
            return []
    
    def resolve(self, service_type: Type[T], scope_id: Optional[str] = None) -> T:
        """Resolve a service instance"""
        if service_type not in self._services:
            raise DependencyInjectionError(f"Service not registered: {service_type.__name__}")
        
        descriptor = self._services[service_type]
        
        # Check for circular dependencies
        if service_type in self._building:
            raise DependencyInjectionError(f"Circular dependency detected: {service_type.__name__}")
        
        try:
            self._building.add(service_type)
            
            if descriptor.lifetime == ServiceLifetime.SINGLETON:
                return self._resolve_singleton(service_type, descriptor)
            elif descriptor.lifetime == ServiceLifetime.SCOPED:
                return self._resolve_scoped(service_type, descriptor, scope_id)
            else:  # TRANSIENT
                return self._resolve_transient(service_type, descriptor, scope_id)
        
        finally:
            self._building.discard(service_type)
    
    def _resolve_singleton(self, service_type: Type[T], descriptor: ServiceDescriptor) -> T:
        """Resolve singleton instance"""
        if service_type not in self._instances:
            self._instances[service_type] = self._create_instance(descriptor)
        
        return self._instances[service_type]
    
    def _resolve_scoped(self, service_type: Type[T], descriptor: ServiceDescriptor, 
                       scope_id: Optional[str]) -> T:
        """Resolve scoped instance"""
        if not scope_id:
            scope_id = "default"
        
        if scope_id not in self._scoped_instances:
            self._scoped_instances[scope_id] = {}
        
        scope_instances = self._scoped_instances[scope_id]
        
        if service_type not in scope_instances:
            scope_instances[service_type] = self._create_instance(descriptor, scope_id)
        
        return scope_instances[service_type]
    
    def _resolve_transient(self, service_type: Type[T], descriptor: ServiceDescriptor,
                          scope_id: Optional[str]) -> T:
        """Resolve transient instance"""
        return self._create_instance(descriptor, scope_id)
    
    def _create_instance(self, descriptor: ServiceDescriptor, scope_id: Optional[str] = None) -> Any:
        """Create service instance with dependency injection"""
        try:
            # Use factory if provided
            if descriptor.factory:
                return descriptor.factory()
            
            # Resolve dependencies
            resolved_dependencies = []
            for dep_type in descriptor.dependencies:
                dependency = self.resolve(dep_type, scope_id)
                resolved_dependencies.append(dependency)
            
            # Create instance
            instance = descriptor.implementation_type(*resolved_dependencies)
            
            logging.debug(f"✅ Created instance: {descriptor.service_type.__name__}")
            return instance
            
        except Exception as e:
            raise DependencyInjectionError(
                f"Failed to create instance of {descriptor.service_type.__name__}: {str(e)}"
            )
    
    def create_scope(self, scope_id: str) -> 'ServiceScope':
        """Create a new service scope"""
        return ServiceScope(self, scope_id)
    
    def dispose_scope(self, scope_id: str):
        """Dispose of a service scope"""
        if scope_id in self._scoped_instances:
            scope_instances = self._scoped_instances[scope_id]
            
            # Call dispose on disposable services
            for instance in scope_instances.values():
                if hasattr(instance, 'dispose'):
                    try:
                        instance.dispose()
                    except Exception as e:
                        logging.error(f"❌ Error disposing service: {e}")
            
            del self._scoped_instances[scope_id]
            logging.debug(f"🗑️ Disposed scope: {scope_id}")
    
    def get_service_info(self) -> Dict[str, Any]:
        """Get information about registered services"""
        return {
            "registered_services": len(self._services),
            "singleton_instances": len(self._instances),
            "active_scopes": len(self._scoped_instances),
            "services": [
                {
                    "type": service_type.__name__,
                    "implementation": descriptor.implementation_type.__name__ if descriptor.implementation_type else "Factory",
                    "lifetime": descriptor.lifetime.value,
                    "dependencies": [dep.__name__ for dep in descriptor.dependencies]
                }
                for service_type, descriptor in self._services.items()
            ]
        }


class ServiceScope:
    """
    Service scope for managing scoped service lifetimes
    """
    
    def __init__(self, container: ServiceContainer, scope_id: str):
        self.container = container
        self.scope_id = scope_id
    
    def resolve(self, service_type: Type[T]) -> T:
        """Resolve service within this scope"""
        return self.container.resolve(service_type, self.scope_id)
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.container.dispose_scope(self.scope_id)


# =============================================================================
# SERVICE INTERFACES
# =============================================================================

class IPriceFetcher(ABC):
    """Interface for price fetching services"""
    
    @abstractmethod
    async def get_price(self, token_address: str) -> Optional[float]:
        """Get current price for a token"""
        pass
    
    @abstractmethod
    async def get_multiple_prices(self, token_addresses: List[str]) -> Dict[str, float]:
        """Get prices for multiple tokens"""
        pass


class IRiskManager(ABC):
    """Interface for risk management services"""
    
    @abstractmethod
    async def check_position_risk(self, position_id: int) -> Dict[str, Any]:
        """Check risk for a specific position"""
        pass
    
    @abstractmethod
    async def get_portfolio_risk(self) -> Dict[str, Any]:
        """Get overall portfolio risk metrics"""
        pass


class INotificationService(ABC):
    """Interface for notification services"""
    
    @abstractmethod
    async def send_notification(self, message: str, priority: int = 2) -> bool:
        """Send a notification"""
        pass
    
    @abstractmethod
    async def send_alert(self, alert_type: str, details: Dict[str, Any]) -> bool:
        """Send an alert notification"""
        pass


class IPositionManager(ABC):
    """Interface for position management"""
    
    @abstractmethod
    async def create_position(self, candidate: Dict[str, Any]) -> int:
        """Create a new trading position"""
        pass
    
    @abstractmethod
    async def close_position(self, position_id: int, reason: str) -> bool:
        """Close a trading position"""
        pass
    
    @abstractmethod
    async def get_open_positions(self) -> List[Dict[str, Any]]:
        """Get all open positions"""
        pass


class IDataSource(ABC):
    """Interface for data sources"""
    
    @abstractmethod
    async def fetch_unlock_events(self) -> List[Dict[str, Any]]:
        """Fetch unlock events"""
        pass
    
    @abstractmethod
    async def validate_data(self, data: Dict[str, Any]) -> bool:
        """Validate data quality"""
        pass


# =============================================================================
# SERVICE FACTORY
# =============================================================================

class ServiceFactory:
    """
    Factory for creating and configuring services
    """
    
    @staticmethod
    def create_container() -> ServiceContainer:
        """Create and configure the main service container"""
        container = ServiceContainer()
        
        # Register core services (implementations would be provided by each service)
        # These are examples - actual implementations would be in their respective modules
        
        logging.info("🏭 Service factory created container")
        return container
    
    @staticmethod
    def configure_trading_services(container: ServiceContainer):
        """Configure trading-specific services"""
        # This would be called by the main application to register
        # trading-specific service implementations
        pass
    
    @staticmethod
    def configure_data_services(container: ServiceContainer):
        """Configure data-specific services"""
        # This would be called to register data service implementations
        pass


# =============================================================================
# DECORATORS
# =============================================================================

def injectable(service_type: Type):
    """Decorator to mark a class as injectable"""
    def decorator(cls):
        cls._injectable_type = service_type
        return cls
    return decorator


def inject(service_type: Type):
    """Decorator for dependency injection"""
    def decorator(func):
        if not hasattr(func, '_injected_dependencies'):
            func._injected_dependencies = []
        func._injected_dependencies.append(service_type)
        return func
    return decorator


# Global container instance
_global_container: Optional[ServiceContainer] = None


def get_container() -> ServiceContainer:
    """Get the global service container"""
    global _global_container
    
    if _global_container is None:
        _global_container = ServiceFactory.create_container()
    
    return _global_container


def set_container(container: ServiceContainer):
    """Set the global service container"""
    global _global_container
    _global_container = container


# Export public interface
__all__ = [
    'ServiceContainer',
    'ServiceScope',
    'ServiceLifetime',
    'ServiceDescriptor',
    'ServiceFactory',
    'DependencyInjectionError',
    
    # Interfaces
    'IPriceFetcher',
    'IRiskManager', 
    'INotificationService',
    'IPositionManager',
    'IDataSource',
    
    # Decorators
    'injectable',
    'inject',
    
    # Global functions
    'get_container',
    'set_container'
]
