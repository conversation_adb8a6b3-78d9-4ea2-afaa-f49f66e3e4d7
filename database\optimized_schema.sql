-- Optimized Database Schema with Performance Enhancements
-- Project Chimera - High Performance Trading System
-- 
-- Features:
-- - Composite indexes for common query patterns
-- - Partitioning for large tables
-- - Materialized views for complex aggregations
-- - Optimized data types and constraints
-- - Performance monitoring views

-- =============================================================================
-- PERFORMANCE CONFIGURATION
-- =============================================================================

-- Enable query performance tracking
CREATE EXTENSION IF NOT EXISTS pg_stat_statements;

-- Enable parallel query execution
SET max_parallel_workers_per_gather = 4;
SET parallel_tuple_cost = 0.1;
SET parallel_setup_cost = 1000.0;

-- =============================================================================
-- OPTIMIZED CORE TABLES
-- =============================================================================

-- Enhanced unlock_events table with better indexing
CREATE TABLE IF NOT EXISTS unlock_events (
    id SERIAL PRIMARY KEY,
    token_symbol VARCHAR(20) NOT NULL,
    contract_address VARCHAR(42) NOT NULL,
    unlock_date TIMESTAMP WITH TIME ZONE NOT NULL,
    unlock_amount DECIMAL(20,8) NOT NULL CHECK (unlock_amount > 0),
    circulating_supply DECIMAL(20,8) CHECK (circulating_supply > 0),
    total_supply DECIMAL(20,8) CHECK (total_supply > 0),
    pressure_score DECIMAL(8,4) CHECK (pressure_score >= 0),
    volume_24h DECIMAL(20,8) CHECK (volume_24h >= 0),
    market_cap DECIMAL(20,8) CHECK (market_cap >= 0),
    source VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_supply_ratio CHECK (
        circulating_supply IS NULL OR 
        total_supply IS NULL OR 
        circulating_supply <= total_supply
    ),
    CONSTRAINT unique_unlock_event UNIQUE(contract_address, unlock_date)
);

-- Enhanced positions table with better performance
CREATE TABLE IF NOT EXISTS positions (
    position_id SERIAL PRIMARY KEY,
    token_symbol VARCHAR(20) NOT NULL,
    token_address VARCHAR(42) NOT NULL,
    amount_shorted DECIMAL(20,8) NOT NULL CHECK (amount_shorted > 0),
    entry_price_in_usdc DECIMAL(20,8) NOT NULL CHECK (entry_price_in_usdc > 0),
    current_price DECIMAL(20,8) CHECK (current_price > 0),
    
    -- Position status and timing
    status VARCHAR(20) DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'CLOSED', 'PENDING', 'FAILED')),
    entry_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    close_time TIMESTAMP WITH TIME ZONE,
    unlock_date TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Risk management
    stop_loss_price DECIMAL(20,8) CHECK (stop_loss_price > 0),
    take_profit_price DECIMAL(20,8) CHECK (take_profit_price > 0),
    
    -- P&L tracking
    pnl_usd DECIMAL(20,8),
    pnl_percentage DECIMAL(8,4),
    close_price DECIMAL(20,8) CHECK (close_price > 0),
    close_reason TEXT,
    
    -- Transaction tracking
    borrow_tx_hash VARCHAR(66),
    swap_tx_hash VARCHAR(66),
    repay_tx_hash VARCHAR(66),
    close_swap_tx_hash VARCHAR(66),
    
    -- Strategy metadata
    strategy_id VARCHAR(50) DEFAULT 'pre_unlock_decay_v1',
    pressure_score DECIMAL(8,4),
    notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints for short positions
    CONSTRAINT valid_short_prices CHECK (
        stop_loss_price IS NULL OR 
        entry_price_in_usdc IS NULL OR 
        stop_loss_price > entry_price_in_usdc
    ),
    CONSTRAINT valid_take_profit CHECK (
        take_profit_price IS NULL OR 
        entry_price_in_usdc IS NULL OR 
        take_profit_price < entry_price_in_usdc
    )
);

-- Partitioned price_history table for better performance
CREATE TABLE IF NOT EXISTS price_history (
    id BIGSERIAL,
    token_address VARCHAR(42) NOT NULL,
    price_usd DECIMAL(20,8) NOT NULL CHECK (price_usd > 0),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source VARCHAR(50) DEFAULT 'unknown',
    volume_24h DECIMAL(20,8),
    market_cap DECIMAL(20,8),
    
    PRIMARY KEY (id, timestamp)
) PARTITION BY RANGE (timestamp);

-- Create monthly partitions for price_history (last 6 months + next 6 months)
DO $$
DECLARE
    start_date DATE;
    end_date DATE;
    partition_name TEXT;
BEGIN
    -- Create partitions for the last 6 months and next 6 months
    FOR i IN -6..6 LOOP
        start_date := DATE_TRUNC('month', NOW() + INTERVAL '1 month' * i);
        end_date := start_date + INTERVAL '1 month';
        partition_name := 'price_history_' || TO_CHAR(start_date, 'YYYY_MM');
        
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS %I PARTITION OF price_history
            FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );
    END LOOP;
END $$;

-- Risk alerts table for real-time monitoring
CREATE TABLE IF NOT EXISTS risk_alerts (
    alert_id SERIAL PRIMARY KEY,
    position_id INTEGER REFERENCES positions(position_id),
    alert_type VARCHAR(50) NOT NULL,
    risk_level VARCHAR(20) NOT NULL,
    current_price DECIMAL(20,8) NOT NULL,
    trigger_price DECIMAL(20,8) NOT NULL,
    message TEXT NOT NULL,
    action_required VARCHAR(100),
    resolved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- =============================================================================
-- PERFORMANCE INDEXES
-- =============================================================================

-- Unlock events indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_unlock_events_date_score 
ON unlock_events(unlock_date, pressure_score DESC) 
WHERE pressure_score >= 0.5;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_unlock_events_contract 
ON unlock_events(contract_address);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_unlock_events_upcoming 
ON unlock_events(unlock_date) 
WHERE unlock_date > NOW();

-- Positions indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_positions_status_token 
ON positions(status, token_address) 
WHERE status = 'OPEN';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_positions_unlock_date 
ON positions(unlock_date) 
WHERE status = 'OPEN';

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_positions_entry_time 
ON positions(entry_time DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_positions_pnl 
ON positions(pnl_percentage) 
WHERE status = 'OPEN' AND pnl_percentage IS NOT NULL;

-- Price history indexes (applied to all partitions)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_price_history_token_time 
ON price_history(token_address, timestamp DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_price_history_latest 
ON price_history(token_address, timestamp DESC) 
WHERE timestamp > NOW() - INTERVAL '1 hour';

-- Risk alerts indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_risk_alerts_position 
ON risk_alerts(position_id, created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_risk_alerts_unresolved 
ON risk_alerts(created_at DESC) 
WHERE resolved = FALSE;

-- =============================================================================
-- MATERIALIZED VIEWS FOR PERFORMANCE
-- =============================================================================

-- Portfolio summary view
CREATE MATERIALIZED VIEW IF NOT EXISTS portfolio_summary AS
SELECT 
    COUNT(*) FILTER (WHERE status = 'OPEN') as open_positions,
    COUNT(*) FILTER (WHERE status = 'CLOSED') as closed_positions,
    COALESCE(SUM(pnl_usd) FILTER (WHERE status = 'CLOSED'), 0) as realized_pnl,
    COALESCE(AVG(pnl_percentage) FILTER (WHERE status = 'CLOSED'), 0) as avg_pnl_pct,
    COUNT(*) FILTER (WHERE status = 'CLOSED' AND pnl_usd > 0) as winning_trades,
    COUNT(*) FILTER (WHERE status = 'CLOSED' AND pnl_usd < 0) as losing_trades,
    MAX(updated_at) as last_updated
FROM positions;

-- Create unique index for materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_portfolio_summary_refresh 
ON portfolio_summary(last_updated);

-- Token performance view
CREATE MATERIALIZED VIEW IF NOT EXISTS token_performance AS
SELECT 
    token_symbol,
    token_address,
    COUNT(*) as total_trades,
    COUNT(*) FILTER (WHERE status = 'CLOSED') as closed_trades,
    COALESCE(AVG(pnl_percentage) FILTER (WHERE status = 'CLOSED'), 0) as avg_pnl_pct,
    COALESCE(SUM(pnl_usd) FILTER (WHERE status = 'CLOSED'), 0) as total_pnl,
    MAX(pressure_score) as max_pressure_score,
    AVG(pressure_score) as avg_pressure_score,
    MAX(updated_at) as last_updated
FROM positions
GROUP BY token_symbol, token_address;

-- Create unique index for token performance view
CREATE UNIQUE INDEX IF NOT EXISTS idx_token_performance_symbol 
ON token_performance(token_symbol, token_address);

-- Recent price movements view
CREATE MATERIALIZED VIEW IF NOT EXISTS recent_price_movements AS
SELECT DISTINCT ON (token_address)
    token_address,
    price_usd as current_price,
    timestamp as last_update,
    LAG(price_usd) OVER (PARTITION BY token_address ORDER BY timestamp) as previous_price,
    (price_usd - LAG(price_usd) OVER (PARTITION BY token_address ORDER BY timestamp)) / 
    LAG(price_usd) OVER (PARTITION BY token_address ORDER BY timestamp) * 100 as price_change_pct
FROM price_history
WHERE timestamp > NOW() - INTERVAL '24 hours'
ORDER BY token_address, timestamp DESC;

-- Create unique index for price movements view
CREATE UNIQUE INDEX IF NOT EXISTS idx_recent_price_movements_token 
ON recent_price_movements(token_address);

-- =============================================================================
-- PERFORMANCE MONITORING VIEWS
-- =============================================================================

-- Query performance monitoring
CREATE OR REPLACE VIEW query_performance AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    max_time,
    stddev_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
WHERE calls > 10
ORDER BY total_time DESC;

-- Table size monitoring
CREATE OR REPLACE VIEW table_sizes AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Index usage monitoring
CREATE OR REPLACE VIEW index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- =============================================================================
-- FUNCTIONS FOR MAINTENANCE
-- =============================================================================

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_materialized_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY portfolio_summary;
    REFRESH MATERIALIZED VIEW CONCURRENTLY token_performance;
    REFRESH MATERIALIZED VIEW CONCURRENTLY recent_price_movements;
    
    -- Log the refresh
    INSERT INTO system_logs (log_level, message, created_at)
    VALUES ('INFO', 'Materialized views refreshed', NOW());
END;
$$ LANGUAGE plpgsql;

-- Function to clean old price history data
CREATE OR REPLACE FUNCTION cleanup_old_price_data(retention_days INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM price_history 
    WHERE timestamp < NOW() - INTERVAL '1 day' * retention_days;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup
    INSERT INTO system_logs (log_level, message, created_at)
    VALUES ('INFO', format('Cleaned up %s old price records', deleted_count), NOW());
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to analyze table statistics
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS void AS $$
BEGIN
    ANALYZE unlock_events;
    ANALYZE positions;
    ANALYZE price_history;
    ANALYZE risk_alerts;
    
    -- Log the analysis
    INSERT INTO system_logs (log_level, message, created_at)
    VALUES ('INFO', 'Table statistics updated', NOW());
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- AUTOMATED MAINTENANCE
-- =============================================================================

-- Create system logs table for maintenance tracking
CREATE TABLE IF NOT EXISTS system_logs (
    log_id SERIAL PRIMARY KEY,
    log_level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for system logs
CREATE INDEX IF NOT EXISTS idx_system_logs_time 
ON system_logs(created_at DESC);

-- =============================================================================
-- PERFORMANCE OPTIMIZATION SETTINGS
-- =============================================================================

-- Update table statistics automatically
ALTER TABLE unlock_events SET (autovacuum_analyze_scale_factor = 0.05);
ALTER TABLE positions SET (autovacuum_analyze_scale_factor = 0.05);
ALTER TABLE price_history SET (autovacuum_analyze_scale_factor = 0.02);

-- Set fill factor for tables with frequent updates
ALTER TABLE positions SET (fillfactor = 90);
ALTER TABLE price_history SET (fillfactor = 95);

-- =============================================================================
-- GRANTS AND PERMISSIONS
-- =============================================================================

-- Grant permissions to application user
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO chimera_app;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO chimera_app;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO chimera_app;

-- Grant read-only access to monitoring user
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO chimera_monitor;
-- GRANT SELECT ON query_performance, table_sizes, index_usage TO chimera_monitor;

-- =============================================================================
-- INITIAL DATA AND VALIDATION
-- =============================================================================

-- Insert initial system log entry
INSERT INTO system_logs (log_level, message) 
VALUES ('INFO', 'Optimized database schema initialized')
ON CONFLICT DO NOTHING;

-- Validate schema integrity
DO $$
BEGIN
    -- Check if all required indexes exist
    IF NOT EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'idx_positions_status_token') THEN
        RAISE EXCEPTION 'Critical index missing: idx_positions_status_token';
    END IF;
    
    -- Check if materialized views exist
    IF NOT EXISTS (SELECT 1 FROM pg_matviews WHERE matviewname = 'portfolio_summary') THEN
        RAISE EXCEPTION 'Critical materialized view missing: portfolio_summary';
    END IF;
    
    -- Log successful validation
    INSERT INTO system_logs (log_level, message) 
    VALUES ('INFO', 'Schema validation completed successfully');
END $$;
