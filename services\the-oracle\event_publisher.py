"""
Oracle Event Publisher - Updated to use Unified Redis Manager
============================================================

This module has been updated to use the unified Redis manager,
eliminating duplicate Redis connection and publishing code.
"""

import logging
from typing import Dict, Any

# Import unified Redis operations
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'common'))

from redis_manager import get_redis_publisher
from unified_utils import LoggingUtils

def publish_unlock_event(event: Dict[str, Any]):
    """Publishes a token unlock event using unified Redis publisher"""
    try:
        publisher = get_redis_publisher()
        success = publisher.publish_unlock_event(event)

        if success:
            LoggingUtils.log_operation_success("Publish unlock event", {
                "token_symbol": event.get('token_symbol', 'Unknown'),
                "unlock_date": event.get('unlock_date')
            })
        else:
            logging.warning(f"Failed to publish unlock event for {event.get('token_symbol', 'Unknown')}")

    except Exception as e:
        LoggingUtils.log_operation_failure("Publish unlock event", e, {
            "token_symbol": event.get('token_symbol', 'Unknown')
        })
        raise

def publish_position_opened(position_data: Dict[str, Any]):
    """Publishes a position opened event using unified Redis publisher"""
    try:
        publisher = get_redis_publisher()
        success = publisher.publish_position_opened(position_data)

        if success:
            LoggingUtils.log_operation_success("Publish position opened", {
                "token_symbol": position_data.get('token_symbol', 'Unknown')
            })

    except Exception as e:
        LoggingUtils.log_operation_failure("Publish position opened", e)
        raise

def publish_close_position(position_data: Dict[str, Any]):
    """Publishes a close position event using unified Redis publisher"""
    try:
        publisher = get_redis_publisher()
        success = publisher.publish_close_position_signal(position_data)

        if success:
            LoggingUtils.log_operation_success("Publish close position", {
                "position_id": position_data.get('position_id', 'Unknown')
            })

    except Exception as e:
        LoggingUtils.log_operation_failure("Publish close position", e)
        raise

def publish_trade_candidate(candidate_data: Dict[str, Any]):
    """Publishes a trade candidate event using unified Redis publisher"""
    try:
        publisher = get_redis_publisher()
        success = publisher.publish_trade_candidate(candidate_data)

        if success:
            LoggingUtils.log_operation_success("Publish trade candidate", {
                "token_symbol": candidate_data.get('token_symbol', 'Unknown')
            })

    except Exception as e:
        LoggingUtils.log_operation_failure("Publish trade candidate", e)
        raise
