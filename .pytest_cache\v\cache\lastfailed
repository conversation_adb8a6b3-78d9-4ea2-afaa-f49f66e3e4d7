{"tests/test_oracle_modern.py::TestCoinGeckoIntegration::test_fetch_from_coingecko_api_success": true, "tests/test_async_database.py": true, "tests/test_async_database_simple.py::TestAsyncDatabasePoolSimple::test_pool_initialization": true, "tests/test_async_database_simple.py::TestAsyncDatabasePoolSimple::test_execute_query_success": true, "tests/test_async_database_simple.py::TestAsyncDatabasePoolSimple::test_execute_query_error": true, "tests/test_async_database_simple.py::TestAsyncDatabasePoolSimple::test_execute_batch_success": true, "tests/test_async_database_simple.py::TestAsyncDatabasePoolSimple::test_fetch_modes": true, "tests/test_async_database_simple.py::TestAsyncDatabaseConcurrency::test_concurrent_queries": true, "tests/test_async_database_simple.py::TestAsyncDatabaseConcurrency::test_connection_pool_behavior": true, "tests/test_secure_config.py::TestSecretManager": true, "tests/test_secure_config.py::TestConfigurationValidation": true, "tests/test_secure_config.py::TestSecureConfigManager": true, "tests/test_secure_config.py::TestSecureConfigurationManager": true, "tests/test_secure_config.py::TestGlobalConfigurationFunctions": true, "tests/test_secure_config.py::TestConfigurationSecurity": true, "tests/test_secure_config.py::TestConfigurationValidation::test_environment_variable_validation": true, "tests/test_secure_config.py::TestConfigurationValidation::test_missing_environment_variables": true, "tests/test_secure_config.py::TestConfigurationValidation::test_encryption_setup_validation": true, "tests/test_secure_config.py::TestSecureConfigurationManager::test_environment_detection": true, "tests/test_secure_config.py::TestSecureConfigurationManager::test_health_check": true, "tests/test_secure_config.py::TestGlobalConfigurationFunctions::test_singleton_behavior": true}