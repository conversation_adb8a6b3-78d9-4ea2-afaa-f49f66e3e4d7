"""
Enhanced Secure Configuration Management - 2025 Security Best Practices
======================================================================

Advanced configuration management with security features:
- Encrypted secret management
- Input validation and sanitization
- Security audit logging
- Environment-based configuration
- Type-safe configuration classes
- Configuration validation and error reporting
"""

import os
import logging
from enum import Enum
from typing import Optional, Dict, Any, List, Union
from dataclasses import dataclass, field
from decimal import Decimal
from datetime import datetime, timezone

from .security import SecretManager, InputValidator, SecurityError, audit_logger
from .constants import TradingConstants, SystemConstants, ValidationConstants


class Environment(Enum):
    """Deployment environment types"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class ConfigurationError(Exception):
    """Configuration-related error"""
    pass


@dataclass
class SecureDatabaseConfig:
    """Secure database configuration with validation"""
    url: str = field(default="")
    pool_size: int = field(default=10)
    max_overflow: int = field(default=20)
    pool_timeout: int = field(default=30)
    ssl_required: bool = field(default=True)
    connection_timeout: int = field(default=30)
    
    def __post_init__(self):
        # Get encrypted database URL
        secret_manager = SecretManager()
        self.url = secret_manager.get_secret("DATABASE_URL") or self.url
        
        if not self.url:
            raise ConfigurationError("DATABASE_URL is required")
        
        # Validate URL format (basic check)
        if not self.url.startswith(('postgresql://', 'postgres://')):
            raise ConfigurationError("Invalid database URL format")
        
        # Validate numeric parameters
        if self.pool_size < 1 or self.pool_size > 100:
            raise ConfigurationError("Pool size must be between 1 and 100")
        
        if self.max_overflow < 0 or self.max_overflow > 200:
            raise ConfigurationError("Max overflow must be between 0 and 200")
        
        # Log configuration (without sensitive data)
        audit_logger.log_security_event(
            "DATABASE_CONFIG_LOADED",
            {
                "pool_size": self.pool_size,
                "ssl_required": self.ssl_required,
                "has_url": bool(self.url)
            }
        )


@dataclass
class SecureRedisConfig:
    """Secure Redis configuration with validation"""
    url: str = field(default="")
    max_connections: int = field(default=50)
    socket_timeout: int = field(default=30)
    socket_connect_timeout: int = field(default=30)
    ssl_required: bool = field(default=False)
    
    def __post_init__(self):
        # Get encrypted Redis URL
        secret_manager = SecretManager()
        self.url = secret_manager.get_secret("REDIS_URL") or self.url
        
        if not self.url:
            raise ConfigurationError("REDIS_URL is required")
        
        # Validate URL format
        if not self.url.startswith(('redis://', 'rediss://')):
            raise ConfigurationError("Invalid Redis URL format")
        
        # Validate parameters
        if self.max_connections < 1 or self.max_connections > 1000:
            raise ConfigurationError("Max connections must be between 1 and 1000")
        
        audit_logger.log_security_event(
            "REDIS_CONFIG_LOADED",
            {
                "max_connections": self.max_connections,
                "ssl_required": self.ssl_required,
                "has_url": bool(self.url)
            }
        )


@dataclass
class SecureAPIConfig:
    """Secure API configuration with validation"""
    infura_api_key: str = field(default="")
    coingecko_api_key: str = field(default="")
    telegram_bot_token: str = field(default="")
    telegram_chat_id: str = field(default="")
    
    # Rate limiting
    rate_limit_per_second: int = field(default=100)
    rate_limit_burst: int = field(default=200)
    
    # Security settings
    api_timeout_seconds: int = field(default=30)
    max_retries: int = field(default=3)
    require_api_key: bool = field(default=True)
    
    def __post_init__(self):
        secret_manager = SecretManager()
        
        # Load encrypted API keys
        self.infura_api_key = secret_manager.get_secret("INFURA_API_KEY") or self.infura_api_key
        self.coingecko_api_key = secret_manager.get_secret("COINGECKO_API_KEY") or self.coingecko_api_key
        self.telegram_bot_token = secret_manager.get_secret("TELEGRAM_BOT_TOKEN") or self.telegram_bot_token
        self.telegram_chat_id = secret_manager.get_secret("TELEGRAM_CHAT_ID") or self.telegram_chat_id
        
        # Validate API keys format (basic validation)
        if self.infura_api_key and len(self.infura_api_key) < 20:
            raise ConfigurationError("Invalid Infura API key format")
        
        if self.telegram_bot_token and not self.telegram_bot_token.count(':') == 1:
            raise ConfigurationError("Invalid Telegram bot token format")
        
        # Validate rate limiting
        if self.rate_limit_per_second < 1 or self.rate_limit_per_second > 10000:
            raise ConfigurationError("Rate limit must be between 1 and 10000")
        
        audit_logger.log_security_event(
            "API_CONFIG_LOADED",
            {
                "has_infura_key": bool(self.infura_api_key),
                "has_coingecko_key": bool(self.coingecko_api_key),
                "has_telegram_token": bool(self.telegram_bot_token),
                "rate_limit": self.rate_limit_per_second,
                "require_api_key": self.require_api_key
            }
        )


@dataclass
class SecureTradingConfig:
    """Secure trading configuration with validation"""
    paper_trading_mode: bool = field(default=True)
    stop_loss_pct: Decimal = field(default=TradingConstants.DEFAULT_STOP_LOSS_PCT)
    take_profit_pct: Decimal = field(default=TradingConstants.DEFAULT_TAKE_PROFIT_PCT)
    position_size_usd: Decimal = field(default=TradingConstants.DEFAULT_POSITION_SIZE_USD)
    max_positions: int = field(default=10)
    pressure_score_threshold: float = field(default=0.75)
    
    # Risk management
    max_daily_loss_usd: Decimal = field(default=Decimal("5000"))
    max_position_size_usd: Decimal = field(default=TradingConstants.MAX_POSITION_SIZE_USD)
    monitoring_interval_seconds: int = field(default=60)
    
    # Private key management
    private_key: str = field(default="")
    wallet_address: str = field(default="")
    
    def __post_init__(self):
        secret_manager = SecretManager()
        
        # Load encrypted private key
        self.private_key = secret_manager.get_secret("PRIVATE_KEY") or self.private_key
        self.wallet_address = secret_manager.get_secret("WALLET_ADDRESS") or self.wallet_address
        
        # Validate trading parameters
        if self.stop_loss_pct <= 0 or self.stop_loss_pct >= 1:
            raise ConfigurationError("Stop loss percentage must be between 0 and 1")
        
        if self.take_profit_pct <= 0 or self.take_profit_pct >= 1:
            raise ConfigurationError("Take profit percentage must be between 0 and 1")
        
        if self.position_size_usd <= 0:
            raise ConfigurationError("Position size must be positive")
        
        if self.max_positions < 1 or self.max_positions > 100:
            raise ConfigurationError("Max positions must be between 1 and 100")
        
        # Validate wallet address if provided
        if self.wallet_address:
            try:
                self.wallet_address = InputValidator.validate_ethereum_address(self.wallet_address)
            except SecurityError as e:
                raise ConfigurationError(f"Invalid wallet address: {e}")
        
        # Validate private key format (basic check)
        if self.private_key and not self.private_key.startswith('0x'):
            if len(self.private_key) != 64:  # 32 bytes = 64 hex chars
                raise ConfigurationError("Invalid private key format")
        
        audit_logger.log_security_event(
            "TRADING_CONFIG_LOADED",
            {
                "paper_trading_mode": self.paper_trading_mode,
                "stop_loss_pct": float(self.stop_loss_pct),
                "take_profit_pct": float(self.take_profit_pct),
                "max_positions": self.max_positions,
                "has_private_key": bool(self.private_key),
                "has_wallet_address": bool(self.wallet_address)
            }
        )


@dataclass
class SecureSystemConfig:
    """Secure system configuration"""
    environment: Environment = field(default=Environment.DEVELOPMENT)
    debug: bool = field(default=False)
    log_level: str = field(default="INFO")
    
    # Security settings
    enable_audit_logging: bool = field(default=True)
    enable_rate_limiting: bool = field(default=True)
    enable_ip_blocking: bool = field(default=True)
    session_timeout_minutes: int = field(default=60)
    
    # Performance settings
    max_concurrent_requests: int = field(default=100)
    request_timeout_seconds: int = field(default=30)
    
    def __post_init__(self):
        # Load environment
        env_str = os.getenv("ENVIRONMENT", "development").lower()
        try:
            self.environment = Environment(env_str)
        except ValueError:
            raise ConfigurationError(f"Invalid environment: {env_str}")
        
        # Load debug setting
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        
        # Validate log level
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        self.log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        if self.log_level not in valid_levels:
            raise ConfigurationError(f"Invalid log level: {self.log_level}")
        
        # Production security checks
        if self.environment == Environment.PRODUCTION:
            if self.debug:
                raise ConfigurationError("Debug mode must be disabled in production")
            
            if not self.enable_audit_logging:
                raise ConfigurationError("Audit logging must be enabled in production")
        
        audit_logger.log_security_event(
            "SYSTEM_CONFIG_LOADED",
            {
                "environment": self.environment.value,
                "debug": self.debug,
                "log_level": self.log_level,
                "audit_logging": self.enable_audit_logging
            }
        )


class SecureConfigManager:
    """
    Centralized secure configuration manager
    """
    
    def __init__(self):
        self.database = SecureDatabaseConfig()
        self.redis = SecureRedisConfig()
        self.api = SecureAPIConfig()
        self.trading = SecureTradingConfig()
        self.system = SecureSystemConfig()
        
        # Validate overall configuration
        self._validate_configuration()
        
        logging.info("✅ Secure configuration loaded successfully")

    def validate_all_configurations(self) -> Dict[str, Any]:
        """Validate all configuration components and return status"""
        validation_results = {
            'database': {'valid': False, 'errors': []},
            'redis': {'valid': False, 'errors': []},
            'api': {'valid': False, 'errors': []},
            'trading': {'valid': False, 'errors': []},
            'system': {'valid': False, 'errors': []}
        }

        # Validate each component
        for component_name, component in [
            ('database', self.database),
            ('redis', self.redis),
            ('api', self.api),
            ('trading', self.trading),
            ('system', self.system)
        ]:
            try:
                # Re-run validation by creating new instance
                component.__post_init__()
                validation_results[component_name]['valid'] = True
            except Exception as e:
                validation_results[component_name]['errors'].append(str(e))

        # Log validation summary
        total_valid = sum(1 for result in validation_results.values() if result['valid'])
        audit_logger.log_security_event(
            "CONFIGURATION_VALIDATION",
            {
                'total_components': len(validation_results),
                'valid_components': total_valid,
                'validation_results': validation_results
            }
        )

        return validation_results

    def get_configuration_summary(self) -> Dict[str, Any]:
        """Get a summary of configuration without sensitive data"""
        return {
            'environment': self.system.environment.value,
            'debug_mode': self.system.debug_mode,
            'paper_trading': self.trading.paper_trading_mode,
            'database_configured': bool(self.database.url),
            'redis_configured': bool(self.redis.url),
            'api_keys_configured': {
                'infura': bool(self.api.infura_api_key),
                'coingecko': bool(self.api.coingecko_api_key),
                'telegram': bool(self.api.telegram_bot_token)
            },
            'security_features': {
                'ssl_database': self.database.ssl_required,
                'ssl_redis': self.redis.ssl_required,
                'api_key_required': self.api.require_api_key,
                'rate_limiting': self.api.rate_limit_per_second
            }
        }

    def export_configuration_template(self) -> str:
        """Export a configuration template for setup"""
        template = """
# Project Chimera - Secure Configuration Template
# ==============================================

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/chimera_db
REDIS_URL=redis://localhost:6379

# API Keys (encrypt these in production)
INFURA_API_KEY=your_infura_api_key_here
COINGECKO_API_KEY=your_coingecko_api_key_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# Trading Configuration
PAPER_TRADING_MODE=true
STOP_LOSS_PCT=0.15
TAKE_PROFIT_PCT=0.10
POSITION_SIZE_USD=1000
MAX_POSITIONS=10
PRESSURE_SCORE_THRESHOLD=0.75

# Security Configuration
MASTER_KEY=generate_secure_master_key_here
PRIVATE_KEY=your_encrypted_private_key_here
WALLET_ADDRESS=your_wallet_address_here

# System Configuration
ENVIRONMENT=development
DEBUG_MODE=true
LOG_LEVEL=INFO
"""
        return template.strip()


class ConfigurationValidator:
    """Advanced configuration validation and health checks"""

    @staticmethod
    def validate_environment_setup() -> Dict[str, Any]:
        """Validate the entire environment setup"""
        results = {
            'environment_variables': ConfigurationValidator._check_environment_variables(),
            'file_permissions': ConfigurationValidator._check_file_permissions(),
            'network_connectivity': ConfigurationValidator._check_network_connectivity(),
            'encryption_setup': ConfigurationValidator._check_encryption_setup()
        }

        return results

    @staticmethod
    def _check_environment_variables() -> Dict[str, Any]:
        """Check required environment variables"""
        required_vars = [
            'DATABASE_URL', 'REDIS_URL', 'INFURA_API_KEY'
        ]

        missing_vars = []
        present_vars = []

        for var in required_vars:
            if os.environ.get(var):
                present_vars.append(var)
            else:
                missing_vars.append(var)

        return {
            'status': 'valid' if not missing_vars else 'invalid',
            'missing_variables': missing_vars,
            'present_variables': present_vars,
            'total_checked': len(required_vars)
        }

    @staticmethod
    def _check_file_permissions() -> Dict[str, Any]:
        """Check file permissions for security"""
        sensitive_files = [
            '.env', 'secrets.json', 'private_key.txt'
        ]

        permission_issues = []

        for file_path in sensitive_files:
            if os.path.exists(file_path):
                stat_info = os.stat(file_path)
                # Check if file is readable by others (security risk)
                if stat_info.st_mode & 0o044:  # Others can read
                    permission_issues.append({
                        'file': file_path,
                        'issue': 'File readable by others',
                        'permissions': oct(stat_info.st_mode)[-3:]
                    })

        return {
            'status': 'valid' if not permission_issues else 'warning',
            'issues': permission_issues
        }

    @staticmethod
    def _check_network_connectivity() -> Dict[str, Any]:
        """Check network connectivity to required services"""
        # This would test actual connectivity in a real implementation
        return {
            'status': 'not_implemented',
            'message': 'Network connectivity checks not implemented'
        }

    @staticmethod
    def _check_encryption_setup() -> Dict[str, Any]:
        """Check encryption setup"""
        try:
            secret_manager = SecretManager()

            # Test encryption/decryption
            test_secret = "test_secret_123"
            encrypted = secret_manager.encrypt_secret(test_secret)
            decrypted = secret_manager.decrypt_secret(encrypted)

            encryption_working = (decrypted == test_secret)

            return {
                'status': 'valid' if encryption_working else 'invalid',
                'encryption_working': encryption_working,
                'has_master_key': bool(secret_manager.master_key)
            }

        except Exception as e:
            return {
                'status': 'invalid',
                'error': str(e),
                'encryption_working': False
            }


class SecureConfigurationManager:
    """Enhanced configuration manager with additional security features"""

    def __init__(self, environment: Optional[Environment] = None):
        self.environment = environment or self._detect_environment()
        self.config = SecureConfigManager()
        self.validator = ConfigurationValidator()

        # Perform initial validation
        self._perform_startup_validation()

    def _detect_environment(self) -> Environment:
        """Auto-detect environment from various sources"""
        env_str = os.environ.get('ENVIRONMENT', 'development').lower()

        try:
            return Environment(env_str)
        except ValueError:
            logging.warning(f"Unknown environment '{env_str}', defaulting to development")
            return Environment.DEVELOPMENT

    def _perform_startup_validation(self):
        """Perform comprehensive startup validation"""
        logging.info("🔍 Performing startup configuration validation...")

        # Validate configuration
        config_results = self.config.validate_all_configurations()

        # Validate environment
        env_results = self.validator.validate_environment_setup()

        # Log results
        total_issues = sum(
            len(result.get('errors', []))
            for result in config_results.values()
        )

        if total_issues > 0:
            logging.warning(f"⚠️ Found {total_issues} configuration issues")
            for component, result in config_results.items():
                if result['errors']:
                    for error in result['errors']:
                        logging.error(f"❌ {component}: {error}")
        else:
            logging.info("✅ All configuration validation passed")

        # Audit log startup validation
        audit_logger.log_security_event(
            "STARTUP_VALIDATION_COMPLETE",
            {
                'environment': self.environment.value,
                'config_issues': total_issues,
                'validation_timestamp': datetime.now(timezone.utc).isoformat()
            }
        )

    def get_config(self) -> SecureConfigManager:
        """Get the secure configuration manager"""
        return self.config

    def reload_configuration(self):
        """Reload configuration from environment"""
        logging.info("🔄 Reloading configuration...")

        # Create new config instance
        self.config = SecureConfigManager()

        # Re-validate
        self._perform_startup_validation()

        logging.info("✅ Configuration reloaded successfully")

    def health_check(self) -> Dict[str, Any]:
        """Perform comprehensive health check"""
        return {
            'configuration': self.config.validate_all_configurations(),
            'environment': self.validator.validate_environment_setup(),
            'summary': self.config.get_configuration_summary(),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }


# Global configuration instance
_global_config_manager: Optional[SecureConfigurationManager] = None


def get_secure_config() -> SecureConfigManager:
    """Get the global secure configuration manager"""
    global _global_config_manager

    if _global_config_manager is None:
        _global_config_manager = SecureConfigurationManager()

    return _global_config_manager.get_config()


def get_configuration_manager() -> SecureConfigurationManager:
    """Get the global configuration manager"""
    global _global_config_manager

    if _global_config_manager is None:
        _global_config_manager = SecureConfigurationManager()

    return _global_config_manager


def reload_global_configuration():
    """Reload the global configuration"""
    global _global_config_manager

    if _global_config_manager:
        _global_config_manager.reload_configuration()
    else:
        _global_config_manager = SecureConfigurationManager()


# Export enhanced interface
__all__ = [
    'Environment',
    'ConfigurationError',
    'SecureDatabaseConfig',
    'SecureRedisConfig',
    'SecureAPIConfig',
    'SecureTradingConfig',
    'SecureSystemConfig',
    'SecureConfigManager',
    'ConfigurationValidator',
    'SecureConfigurationManager',
    'get_secure_config',
    'get_configuration_manager',
    'reload_global_configuration'
]
