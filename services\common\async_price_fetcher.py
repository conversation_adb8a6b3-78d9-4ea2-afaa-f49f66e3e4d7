"""
Async Price Fetcher with Concurrent Operations
==============================================

High-performance price fetching with:
- Concurrent API calls for multiple tokens
- Intelligent caching and fallback mechanisms
- Rate limiting and circuit breaker protection
- WebSocket integration for real-time data
- Batch processing for optimal throughput
"""

import asyncio
import aiohttp
import logging
import time
from typing import Dict, List, Optional, Set, Callable
from decimal import Decimal
from datetime import datetime, timezone
from dataclasses import dataclass, field

from .constants import SystemConstants, MarketConstants
from .error_handling import ChimeraError, CircuitBreaker, RateLimiter, retry_with_backoff
from .async_database import AsyncPriceCache


@dataclass
class PriceSource:
    """Configuration for a price data source"""
    name: str
    base_url: str
    rate_limit: int  # requests per second
    timeout: int = 10
    priority: int = 1  # Lower number = higher priority
    circuit_breaker: CircuitBreaker = field(default_factory=lambda: CircuitBreaker(5, 30))
    rate_limiter: RateLimiter = field(init=False)
    
    def __post_init__(self):
        self.rate_limiter = RateLimiter(self.rate_limit, self.rate_limit)


class AsyncPriceFetcher:
    """
    High-performance async price fetcher with multiple data sources
    """
    
    def __init__(self, price_cache: Optional[AsyncPriceCache] = None):
        self.price_cache = price_cache
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Configure data sources in priority order
        self.sources = [
            PriceSource(
                name="binance",
                base_url="https://api.binance.com/api/v3",
                rate_limit=1200,  # 1200 requests per minute
                priority=1
            ),
            PriceSource(
                name="coingecko",
                base_url="https://api.coingecko.com/api/v3",
                rate_limit=50,   # 50 requests per minute
                priority=2
            ),
            PriceSource(
                name="coinbase",
                base_url="https://api.coinbase.com/v2",
                rate_limit=10000,  # 10000 requests per hour
                priority=3
            )
        ]
        
        # Token symbol mappings for different exchanges
        self.symbol_mappings = {
            "binance": {
                "******************************************": "UNIUSDT",
                "******************************************": "AAVEUSDT",
                "******************************************": "LINKUSDT",
                "0xa0b86a33e6441b8c4505e2c8c5e6e8b8c4505e2c8": "USDCUSDT",
                "******************************************": "ETHUSDT",
                "******************************************": "WBTCUSDT",
            },
            "coingecko": {
                "******************************************": "uniswap",
                "******************************************": "aave",
                "******************************************": "chainlink",
                "0xa0b86a33e6441b8c4505e2c8c5e6e8b8c4505e2c8": "usd-coin",
                "******************************************": "ethereum",
                "******************************************": "wrapped-bitcoin",
            }
        }
        
        # Performance metrics
        self.metrics = {
            'requests_made': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'errors': 0,
            'avg_response_time': 0.0
        }
        
        logging.info("🚀 AsyncPriceFetcher initialized with multiple sources")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.cleanup()
    
    async def initialize(self):
        """Initialize the HTTP session"""
        connector = aiohttp.TCPConnector(
            limit=100,  # Total connection pool size
            limit_per_host=30,  # Per-host connection limit
            ttl_dns_cache=300,  # DNS cache TTL
            use_dns_cache=True,
        )
        
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'ChimeraTrading/1.0',
                'Accept': 'application/json'
            }
        )
        
        logging.info("✅ HTTP session initialized")
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
            logging.info("✅ HTTP session closed")
    
    async def get_price(self, token_address: str) -> Optional[Decimal]:
        """Get price for a single token with caching"""
        start_time = time.time()
        
        try:
            # Check cache first
            if self.price_cache:
                cached_price = await self.price_cache.get_cached_price(token_address)
                if cached_price:
                    self.metrics['cache_hits'] += 1
                    return Decimal(str(cached_price))
            
            self.metrics['cache_misses'] += 1
            
            # Try each source in priority order
            for source in sorted(self.sources, key=lambda x: x.priority):
                try:
                    if not source.rate_limiter.acquire():
                        logging.debug(f"Rate limit exceeded for {source.name}")
                        continue
                    
                    price = await source.circuit_breaker.call(
                        self._fetch_price_from_source, source, token_address
                    )
                    
                    if price:
                        # Update cache
                        if self.price_cache:
                            await self.price_cache.batch_update_prices([{
                                'token_address': token_address,
                                'price': float(price),
                                'source': source.name
                            }])
                        
                        # Update metrics
                        response_time = time.time() - start_time
                        self._update_metrics(response_time)
                        
                        return price
                
                except Exception as e:
                    logging.warning(f"⚠️ {source.name} failed for {token_address}: {e}")
                    continue
            
            logging.error(f"❌ All sources failed for {token_address}")
            self.metrics['errors'] += 1
            return None
            
        except Exception as e:
            logging.error(f"❌ Error fetching price for {token_address}: {e}")
            self.metrics['errors'] += 1
            return None
    
    async def get_multiple_prices(self, token_addresses: List[str], 
                                 max_concurrent: int = 10) -> Dict[str, Decimal]:
        """Get prices for multiple tokens concurrently"""
        if not token_addresses:
            return {}
        
        logging.info(f"📊 Fetching prices for {len(token_addresses)} tokens")
        
        # Create semaphore to limit concurrent requests
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def fetch_with_semaphore(token_address: str) -> tuple[str, Optional[Decimal]]:
            async with semaphore:
                price = await self.get_price(token_address)
                return token_address, price
        
        # Execute all requests concurrently
        tasks = [fetch_with_semaphore(addr) for addr in token_addresses]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        prices = {}
        for result in results:
            if isinstance(result, Exception):
                logging.error(f"❌ Task failed: {result}")
                continue
            
            token_address, price = result
            if price:
                prices[token_address] = price
        
        logging.info(f"✅ Successfully fetched {len(prices)}/{len(token_addresses)} prices")
        return prices
    
    async def _fetch_price_from_source(self, source: PriceSource, 
                                     token_address: str) -> Optional[Decimal]:
        """Fetch price from a specific source"""
        if not self.session:
            raise ChimeraError("HTTP session not initialized")
        
        symbol = self._get_symbol_for_source(source.name, token_address)
        if not symbol:
            return None
        
        if source.name == "binance":
            return await self._fetch_binance_price(symbol)
        elif source.name == "coingecko":
            return await self._fetch_coingecko_price(symbol)
        elif source.name == "coinbase":
            return await self._fetch_coinbase_price(symbol)
        
        return None
    
    async def _fetch_binance_price(self, symbol: str) -> Optional[Decimal]:
        """Fetch price from Binance API"""
        url = f"https://api.binance.com/api/v3/ticker/price?symbol={symbol}"
        
        async with self.session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                return Decimal(data['price'])
            else:
                logging.warning(f"⚠️ Binance API error: {response.status}")
                return None
    
    async def _fetch_coingecko_price(self, coin_id: str) -> Optional[Decimal]:
        """Fetch price from CoinGecko API"""
        url = f"https://api.coingecko.com/api/v3/simple/price?ids={coin_id}&vs_currencies=usd"
        
        async with self.session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                if coin_id in data and 'usd' in data[coin_id]:
                    return Decimal(str(data[coin_id]['usd']))
            else:
                logging.warning(f"⚠️ CoinGecko API error: {response.status}")
                return None
    
    async def _fetch_coinbase_price(self, symbol: str) -> Optional[Decimal]:
        """Fetch price from Coinbase API"""
        url = f"https://api.coinbase.com/v2/exchange-rates?currency={symbol}"
        
        async with self.session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                if 'data' in data and 'rates' in data['data'] and 'USD' in data['data']['rates']:
                    return Decimal(data['data']['rates']['USD'])
            else:
                logging.warning(f"⚠️ Coinbase API error: {response.status}")
                return None
    
    def _get_symbol_for_source(self, source_name: str, token_address: str) -> Optional[str]:
        """Get the appropriate symbol for a given source"""
        mappings = self.symbol_mappings.get(source_name, {})
        return mappings.get(token_address.lower())
    
    def _update_metrics(self, response_time: float):
        """Update performance metrics"""
        self.metrics['requests_made'] += 1
        
        # Update rolling average response time
        current_avg = self.metrics['avg_response_time']
        count = self.metrics['requests_made']
        self.metrics['avg_response_time'] = (current_avg * (count - 1) + response_time) / count
    
    def get_metrics(self) -> Dict[str, any]:
        """Get performance metrics"""
        total_requests = self.metrics['requests_made']
        cache_hit_rate = (
            self.metrics['cache_hits'] / max(1, self.metrics['cache_hits'] + self.metrics['cache_misses'])
        )
        error_rate = self.metrics['errors'] / max(1, total_requests)
        
        return {
            **self.metrics,
            'cache_hit_rate': cache_hit_rate,
            'error_rate': error_rate,
            'sources_status': {
                source.name: {
                    'circuit_breaker_state': source.circuit_breaker.state,
                    'rate_limit_tokens': source.rate_limiter.tokens
                }
                for source in self.sources
            }
        }


class AsyncPriceMonitor:
    """
    Continuous price monitoring with WebSocket integration
    """
    
    def __init__(self, price_fetcher: AsyncPriceFetcher, 
                 update_interval: int = 60):
        self.price_fetcher = price_fetcher
        self.update_interval = update_interval
        self.monitored_tokens: Set[str] = set()
        self.price_callbacks: List[Callable] = []
        self.running = False
        
    def add_token(self, token_address: str):
        """Add token to monitoring list"""
        self.monitored_tokens.add(token_address.lower())
        logging.info(f"📊 Added {token_address} to price monitoring")
    
    def remove_token(self, token_address: str):
        """Remove token from monitoring list"""
        self.monitored_tokens.discard(token_address.lower())
        logging.info(f"📊 Removed {token_address} from price monitoring")
    
    def add_price_callback(self, callback: Callable):
        """Add callback for price updates"""
        self.price_callbacks.append(callback)
    
    async def start_monitoring(self):
        """Start continuous price monitoring"""
        self.running = True
        logging.info(f"📊 Starting price monitoring for {len(self.monitored_tokens)} tokens")
        
        while self.running:
            try:
                if self.monitored_tokens:
                    # Fetch all prices concurrently
                    prices = await self.price_fetcher.get_multiple_prices(
                        list(self.monitored_tokens)
                    )
                    
                    # Notify callbacks
                    for callback in self.price_callbacks:
                        try:
                            await callback(prices)
                        except Exception as e:
                            logging.error(f"❌ Price callback error: {e}")
                
                # Wait for next update
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logging.error(f"❌ Price monitoring error: {e}")
                await asyncio.sleep(self.update_interval)
    
    def stop_monitoring(self):
        """Stop price monitoring"""
        self.running = False
        logging.info("🛑 Price monitoring stopped")


# Export public interface
__all__ = [
    'AsyncPriceFetcher',
    'AsyncPriceMonitor',
    'PriceSource'
]
