"""
Project Chimera - System Constants
=================================

Centralized constants to eliminate magic numbers and improve maintainability.
All configurable values should be defined here with clear documentation.
"""

from decimal import Decimal
from typing import Dict, List

# =============================================================================
# TRADING CONSTANTS
# =============================================================================

class TradingConstants:
    """Trading-related constants and thresholds"""
    
    # Risk Management
    DEFAULT_STOP_LOSS_PCT = Decimal("0.15")          # 15% stop loss
    DEFAULT_TAKE_PROFIT_PCT = Decimal("0.10")        # 10% take profit
    DEFAULT_POSITION_SIZE_USD = Decimal("1000")      # $1000 per position
    MAX_POSITION_SIZE_USD = Decimal("10000")         # Maximum position size
    MIN_POSITION_SIZE_USD = Decimal("100")           # Minimum position size
    
    # Strategy Parameters
    PRESSURE_SCORE_THRESHOLD = 0.75                  # Minimum pressure score for trade
    UNLOCK_MONITORING_DAYS = 14                      # Days ahead to monitor unlocks
    TAKE_PROFIT_DAYS_BEFORE_UNLOCK = 1               # Days before unlock to take profit
    
    # Execution Parameters
    MAX_SLIPPAGE_PCT = Decimal("0.005")              # 0.5% maximum slippage
    EXECUTION_TIMEOUT_SECONDS = 300                  # 5 minutes execution timeout
    PRICE_IMPROVEMENT_THRESHOLD = Decimal("0.002")   # 0.2% price improvement target

# =============================================================================
# MARKET CONDITION CONSTANTS
# =============================================================================

class MarketConstants:
    """Market condition thresholds and parameters"""
    
    # Volatility Thresholds
    HIGH_VOLATILITY_THRESHOLD = 0.02                 # 2% volatility = high risk
    LOW_VOLATILITY_THRESHOLD = 0.01                  # 1% volatility = low risk
    EXTREME_VOLATILITY_THRESHOLD = 0.05              # 5% volatility = extreme risk
    
    # Volume Analysis
    VOLUME_SPIKE_THRESHOLD = 2.0                     # 2x normal volume = spike
    LOW_VOLUME_THRESHOLD = 0.5                       # 0.5x normal volume = low
    
    # Price Movement Thresholds
    SIGNIFICANT_PRICE_CHANGE = 0.01                  # 1% price change
    MAJOR_PRICE_CHANGE = 0.05                       # 5% price change
    EXTREME_PRICE_CHANGE = 0.10                     # 10% price change
    
    # Slippage Risk Levels
    LOW_SLIPPAGE_THRESHOLD = 0.005                   # 0.5% = low slippage risk
    MEDIUM_SLIPPAGE_THRESHOLD = 0.01                 # 1% = medium slippage risk
    HIGH_SLIPPAGE_THRESHOLD = 0.02                   # 2% = high slippage risk

# =============================================================================
# SYSTEM CONFIGURATION CONSTANTS
# =============================================================================

class SystemConstants:
    """System-wide configuration constants"""
    
    # Monitoring Intervals
    DEFAULT_MONITORING_INTERVAL = 60                 # 60 seconds between checks
    FAST_MONITORING_INTERVAL = 30                    # 30 seconds for critical positions
    SLOW_MONITORING_INTERVAL = 300                   # 5 minutes for stable positions
    
    # API Rate Limiting
    DEFAULT_API_RATE_LIMIT = 100                     # 100 requests per second
    BINANCE_API_RATE_LIMIT = 1200                    # 1200 requests per minute
    COINGECKO_API_RATE_LIMIT = 50                    # 50 requests per minute
    
    # Retry Configuration
    DEFAULT_MAX_RETRIES = 3                          # Maximum retry attempts
    DEFAULT_RETRY_DELAY = 1.0                        # Base delay in seconds
    MAX_RETRY_DELAY = 60.0                           # Maximum delay in seconds
    RETRY_BACKOFF_FACTOR = 2.0                       # Exponential backoff factor
    
    # Circuit Breaker
    CIRCUIT_BREAKER_FAILURE_THRESHOLD = 5            # Failures before opening
    CIRCUIT_BREAKER_RECOVERY_TIMEOUT = 30            # Seconds before retry
    
    # Cache Configuration
    PRICE_CACHE_TTL = 300                            # 5 minutes price cache TTL
    DATA_CACHE_TTL = 3600                            # 1 hour data cache TTL
    MAX_PRICE_HISTORY_SIZE = 1000                    # Maximum price history entries

# =============================================================================
# DATABASE CONSTANTS
# =============================================================================

class DatabaseConstants:
    """Database-related constants"""
    
    # Connection Pool
    MIN_POOL_SIZE = 5                                # Minimum connections
    MAX_POOL_SIZE = 20                               # Maximum connections
    POOL_TIMEOUT = 30                                # Connection timeout seconds
    
    # Query Timeouts
    DEFAULT_QUERY_TIMEOUT = 30                       # 30 seconds default timeout
    LONG_QUERY_TIMEOUT = 120                         # 2 minutes for complex queries
    
    # Batch Sizes
    DEFAULT_BATCH_SIZE = 100                         # Default batch insert size
    LARGE_BATCH_SIZE = 1000                          # Large batch operations
    
    # Index Names (for maintenance)
    POSITIONS_STATUS_INDEX = "idx_positions_status_token"
    UNLOCK_EVENTS_DATE_INDEX = "idx_unlock_events_compound"
    PRICE_HISTORY_TOKEN_TIME_INDEX = "idx_price_history_token_time"

# =============================================================================
# BLOCKCHAIN CONSTANTS
# =============================================================================

class BlockchainConstants:
    """Blockchain and DeFi protocol constants"""
    
    # Gas Configuration
    DEFAULT_GAS_LIMIT = 500000                       # Default gas limit
    HIGH_GAS_LIMIT = 1000000                         # High gas limit for complex operations
    GAS_PRICE_MULTIPLIER = 1.1                       # 10% above estimated gas price
    
    # Contract Addresses (Ethereum Mainnet)
    USDC_ADDRESS = "0xA0b86a33E6441b8C4505E2c8C5E6e8b8C4505E2c8"
    WETH_ADDRESS = "******************************************"
    AAVE_LENDING_POOL = "******************************************"
    ONEINCH_ROUTER = "******************************************"
    
    # Token Decimals
    DEFAULT_TOKEN_DECIMALS = 18
    USDC_DECIMALS = 6
    WBTC_DECIMALS = 8
    
    # Transaction Confirmation
    MIN_CONFIRMATIONS = 1                            # Minimum confirmations
    SAFE_CONFIRMATIONS = 3                           # Safe confirmation count

# =============================================================================
# NOTIFICATION CONSTANTS
# =============================================================================

class NotificationConstants:
    """Notification and alerting constants"""
    
    # Message Priorities
    LOW_PRIORITY = 1
    MEDIUM_PRIORITY = 2
    HIGH_PRIORITY = 3
    CRITICAL_PRIORITY = 4
    
    # Telegram Limits
    MAX_MESSAGE_LENGTH = 4096                        # Telegram message limit
    MAX_CAPTION_LENGTH = 1024                        # Telegram caption limit
    
    # Alert Thresholds
    POSITION_RISK_WARNING_PCT = 10                   # 10% loss warning
    POSITION_RISK_CRITICAL_PCT = 12                  # 12% loss critical alert
    SYSTEM_ERROR_THRESHOLD = 5                       # 5 errors before alert

# =============================================================================
# VALIDATION CONSTANTS
# =============================================================================

class ValidationConstants:
    """Input validation constants"""
    
    # Address Validation
    ETHEREUM_ADDRESS_PATTERN = r"^0x[a-fA-F0-9]{40}$"
    TRANSACTION_HASH_PATTERN = r"^0x[a-fA-F0-9]{64}$"
    
    # Token Symbol Validation
    MIN_SYMBOL_LENGTH = 2
    MAX_SYMBOL_LENGTH = 10
    SYMBOL_PATTERN = r"^[A-Z0-9]+$"
    
    # Numeric Validation
    MIN_PRICE = Decimal("0.000001")                  # Minimum valid price
    MAX_PRICE = Decimal("1000000")                   # Maximum valid price
    MIN_AMOUNT = Decimal("0.01")                     # Minimum trade amount
    MAX_AMOUNT = Decimal("1000000")                  # Maximum trade amount

# =============================================================================
# ERROR CODES
# =============================================================================

class ErrorCodes:
    """Standardized error codes for the system"""
    
    # General Errors (1000-1999)
    UNKNOWN_ERROR = 1000
    CONFIGURATION_ERROR = 1001
    VALIDATION_ERROR = 1002
    
    # Data Source Errors (2000-2999)
    DATA_SOURCE_UNAVAILABLE = 2000
    DATA_SOURCE_TIMEOUT = 2001
    DATA_SOURCE_INVALID_RESPONSE = 2002
    DATA_SOURCE_RATE_LIMITED = 2003
    
    # Trading Errors (3000-3999)
    INSUFFICIENT_BALANCE = 3000
    TRADE_EXECUTION_FAILED = 3001
    POSITION_NOT_FOUND = 3002
    RISK_LIMIT_EXCEEDED = 3003
    
    # Blockchain Errors (4000-4999)
    TRANSACTION_FAILED = 4000
    INSUFFICIENT_GAS = 4001
    CONTRACT_CALL_FAILED = 4002
    NETWORK_ERROR = 4003

# =============================================================================
# HELPER FUNCTIONS
# =============================================================================

def get_risk_threshold(risk_level: str) -> Decimal:
    """Get risk threshold based on risk level"""
    thresholds = {
        "LOW": TradingConstants.DEFAULT_STOP_LOSS_PCT * Decimal("0.5"),
        "MEDIUM": TradingConstants.DEFAULT_STOP_LOSS_PCT,
        "HIGH": TradingConstants.DEFAULT_STOP_LOSS_PCT * Decimal("1.5"),
        "EXTREME": TradingConstants.DEFAULT_STOP_LOSS_PCT * Decimal("2.0")
    }
    return thresholds.get(risk_level, TradingConstants.DEFAULT_STOP_LOSS_PCT)

def get_monitoring_interval(position_risk: str) -> int:
    """Get monitoring interval based on position risk"""
    intervals = {
        "LOW": SystemConstants.SLOW_MONITORING_INTERVAL,
        "MEDIUM": SystemConstants.DEFAULT_MONITORING_INTERVAL,
        "HIGH": SystemConstants.FAST_MONITORING_INTERVAL,
        "CRITICAL": SystemConstants.FAST_MONITORING_INTERVAL // 2
    }
    return intervals.get(position_risk, SystemConstants.DEFAULT_MONITORING_INTERVAL)

# =============================================================================
# MAJOR TOKEN ADDRESSES
# =============================================================================

MAJOR_TOKEN_ADDRESSES: Dict[str, str] = {
    # Top DeFi Tokens
    "******************************************": "UNI",      # Uniswap
    "******************************************": "AAVE",     # Aave
    "******************************************": "LINK",     # Chainlink
    "******************************************": "DAI",      # Dai
    "0xa0b86a33e6441b8c4505e2c8c5e6e8b8c4505e2c8": "USDC",     # USD Coin
    "******************************************": "USDT",     # Tether
    "******************************************": "WBTC",     # Wrapped Bitcoin
    "******************************************": "WETH",     # Wrapped Ether
    
    # Additional DeFi Tokens
    "******************************************": "MKR",      # Maker
    "******************************************": "COMP",     # Compound
    "******************************************": "YFI",      # Yearn Finance
    "******************************************": "DPI",      # DeFi Pulse Index
}

# Export all constants for easy importing
__all__ = [
    'TradingConstants',
    'MarketConstants', 
    'SystemConstants',
    'DatabaseConstants',
    'BlockchainConstants',
    'NotificationConstants',
    'ValidationConstants',
    'ErrorCodes',
    'MAJOR_TOKEN_ADDRESSES',
    'get_risk_threshold',
    'get_monitoring_interval'
]
