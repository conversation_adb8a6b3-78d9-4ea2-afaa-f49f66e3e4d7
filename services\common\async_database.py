"""
Async Database Handler with Connection Pooling
==============================================

High-performance async database operations with:
- Connection pooling for optimal resource usage
- Prepared statements for better performance
- Batch operations for bulk inserts/updates
- Query optimization and caching
- Automatic retry and error handling
"""

import asyncio
import asyncpg
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timezone
from decimal import Decimal

from .constants import DatabaseConstants, SystemConstants
from .error_handling import ChimeraError, retry_with_backoff, log_error_with_context
from .models import Position, UnlockEvent, PositionStatus


class AsyncDatabasePool:
    """
    Async database connection pool with optimized queries
    """
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.pool: Optional[asyncpg.Pool] = None
        self._prepared_statements = {}
        
    async def initialize(self):
        """Initialize the connection pool"""
        try:
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=DatabaseConstants.MIN_POOL_SIZE,
                max_size=DatabaseConstants.MAX_POOL_SIZE,
                command_timeout=DatabaseConstants.DEFAULT_QUERY_TIMEOUT,
                server_settings={
                    'application_name': 'chimera_trading_system',
                    'tcp_keepalives_idle': '600',
                    'tcp_keepalives_interval': '30',
                    'tcp_keepalives_count': '3',
                }
            )
            
            # Prepare commonly used statements
            await self._prepare_statements()
            
            logging.info(f"✅ Database pool initialized with {DatabaseConstants.MIN_POOL_SIZE}-{DatabaseConstants.MAX_POOL_SIZE} connections")
            
        except Exception as e:
            logging.error(f"❌ Failed to initialize database pool: {e}")
            raise ChimeraError(f"Database initialization failed: {str(e)}")
    
    async def _prepare_statements(self):
        """Prepare frequently used SQL statements for better performance"""
        statements = {
            'get_open_positions': """
                SELECT p.*, ph.price as current_price
                FROM positions p
                LEFT JOIN LATERAL (
                    SELECT price FROM price_history 
                    WHERE token_address = p.token_address 
                    ORDER BY timestamp DESC LIMIT 1
                ) ph ON true
                WHERE p.status = $1
                ORDER BY p.entry_time DESC
            """,
            
            'get_position_by_id': """
                SELECT * FROM positions WHERE position_id = $1
            """,
            
            'update_position_status': """
                UPDATE positions 
                SET status = $2, close_time = $3, close_reason = $4, 
                    pnl_usd = $5, pnl_percentage = $6, close_price = $7
                WHERE position_id = $1
                RETURNING *
            """,
            
            'insert_position': """
                INSERT INTO positions 
                (token_symbol, token_address, amount_shorted, entry_price_in_usdc, 
                 unlock_date, borrow_tx_hash, swap_tx_hash, strategy_id, pressure_score)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING position_id
            """,
            
            'batch_insert_prices': """
                INSERT INTO price_history (token_address, price_usd, timestamp, source)
                VALUES ($1, $2, $3, $4)
                ON CONFLICT (token_address, timestamp) DO UPDATE SET
                price_usd = EXCLUDED.price_usd, source = EXCLUDED.source
            """,
            
            'get_recent_prices': """
                SELECT token_address, price_usd, timestamp
                FROM price_history
                WHERE token_address = ANY($1) 
                AND timestamp > NOW() - INTERVAL '1 hour'
                ORDER BY timestamp DESC
            """,
            
            'get_upcoming_unlocks': """
                SELECT * FROM unlock_events
                WHERE unlock_date BETWEEN NOW() AND NOW() + INTERVAL '%s days'
                AND pressure_score >= $1
                ORDER BY unlock_date ASC, pressure_score DESC
            """
        }
        
        async with self.pool.acquire() as conn:
            for name, query in statements.items():
                try:
                    await conn.prepare(query)
                    self._prepared_statements[name] = query
                    logging.debug(f"✅ Prepared statement: {name}")
                except Exception as e:
                    logging.warning(f"⚠️ Failed to prepare statement {name}: {e}")
    
    @asynccontextmanager
    async def acquire_connection(self):
        """Context manager for acquiring database connections"""
        if not self.pool:
            raise ChimeraError("Database pool not initialized")
        
        async with self.pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                # Log error with connection context
                log_error_with_context(e, context={'database_operation': True})
                raise
    
    @retry_with_backoff(max_retries=3, base_delay=1.0, exceptions=(asyncpg.PostgresError,))
    async def execute_query(self, query: str, *args, fetch: str = 'none') -> Union[List[Dict], Dict, None]:
        """
        Execute a query with automatic retry and error handling
        
        Args:
            query: SQL query string
            *args: Query parameters
            fetch: 'none', 'one', 'all', or 'val'
        """
        async with self.acquire_connection() as conn:
            try:
                if fetch == 'all':
                    result = await conn.fetch(query, *args)
                    return [dict(row) for row in result]
                elif fetch == 'one':
                    result = await conn.fetchrow(query, *args)
                    return dict(result) if result else None
                elif fetch == 'val':
                    return await conn.fetchval(query, *args)
                else:
                    await conn.execute(query, *args)
                    return None
                    
            except asyncpg.PostgresError as e:
                log_error_with_context(
                    e, 
                    context={
                        'query': query[:100],  # Truncate for logging
                        'args_count': len(args)
                    }
                )
                raise ChimeraError(f"Database query failed: {str(e)}")
    
    async def execute_batch(self, query: str, args_list: List[tuple]) -> None:
        """Execute batch operations for better performance"""
        if not args_list:
            return
        
        async with self.acquire_connection() as conn:
            try:
                await conn.executemany(query, args_list)
                logging.debug(f"✅ Batch executed {len(args_list)} operations")
                
            except asyncpg.PostgresError as e:
                log_error_with_context(
                    e,
                    context={
                        'batch_size': len(args_list),
                        'query': query[:100]
                    }
                )
                raise ChimeraError(f"Batch operation failed: {str(e)}")
    
    async def close(self):
        """Close the connection pool"""
        if self.pool:
            await self.pool.close()
            logging.info("✅ Database pool closed")


class AsyncPositionManager:
    """
    High-performance position management with async operations
    """
    
    def __init__(self, db_pool: AsyncDatabasePool):
        self.db = db_pool
    
    async def create_position(self, position: Position) -> int:
        """Create a new position and return the position ID"""
        query = """
            INSERT INTO positions 
            (token_symbol, token_address, amount_shorted, entry_price_in_usdc, 
             unlock_date, borrow_tx_hash, swap_tx_hash, strategy_id, pressure_score,
             stop_loss_price, take_profit_price)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING position_id
        """
        
        position_id = await self.db.execute_query(
            query,
            position.token_symbol,
            position.token_address,
            float(position.amount_shorted),
            float(position.entry_price_in_usdc),
            position.unlock_date,
            position.borrow_tx_hash,
            position.swap_tx_hash,
            position.strategy_id,
            position.pressure_score,
            float(position.stop_loss_price) if position.stop_loss_price else None,
            float(position.take_profit_price) if position.take_profit_price else None,
            fetch='val'
        )
        
        logging.info(f"✅ Created position {position_id} for {position.token_symbol}")
        return position_id
    
    async def get_open_positions(self) -> List[Dict[str, Any]]:
        """Get all open positions with current prices"""
        query = """
            SELECT p.*, ph.price_usd as current_price
            FROM positions p
            LEFT JOIN LATERAL (
                SELECT price_usd FROM price_history 
                WHERE token_address = p.token_address 
                ORDER BY timestamp DESC LIMIT 1
            ) ph ON true
            WHERE p.status = $1
            ORDER BY p.entry_time DESC
        """
        
        return await self.db.execute_query(query, PositionStatus.OPEN.value, fetch='all')
    
    async def update_position(self, position_id: int, updates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Update position with optimized query"""
        if not updates:
            return None
        
        # Build dynamic update query
        set_clauses = []
        values = []
        param_count = 1
        
        for field, value in updates.items():
            set_clauses.append(f"{field} = ${param_count + 1}")
            values.append(value)
            param_count += 1
        
        query = f"""
            UPDATE positions 
            SET {', '.join(set_clauses)}, updated_at = NOW()
            WHERE position_id = $1
            RETURNING *
        """
        
        return await self.db.execute_query(query, position_id, *values, fetch='one')
    
    async def close_position(self, position_id: int, close_reason: str, 
                           close_price: Decimal, pnl_usd: Decimal, 
                           pnl_percentage: float) -> Optional[Dict[str, Any]]:
        """Close a position with all required fields"""
        query = """
            UPDATE positions 
            SET status = $2, close_time = $3, close_reason = $4, 
                close_price = $5, pnl_usd = $6, pnl_percentage = $7
            WHERE position_id = $1
            RETURNING *
        """
        
        result = await self.db.execute_query(
            query,
            position_id,
            PositionStatus.CLOSED.value,
            datetime.now(timezone.utc),
            close_reason,
            float(close_price),
            float(pnl_usd),
            pnl_percentage,
            fetch='one'
        )
        
        if result:
            logging.info(f"✅ Closed position {position_id}: {close_reason}")
        
        return result
    
    async def get_position_metrics(self, position_id: int) -> Optional[Dict[str, Any]]:
        """Get comprehensive position metrics"""
        query = """
            SELECT 
                p.*,
                ph.price_usd as current_price,
                (ph.price_usd - p.entry_price_in_usdc) * p.amount_shorted as unrealized_pnl,
                ((p.entry_price_in_usdc - ph.price_usd) / p.entry_price_in_usdc) * 100 as pnl_percentage,
                CASE 
                    WHEN ph.price_usd >= p.stop_loss_price THEN 'STOP_LOSS_TRIGGERED'
                    WHEN ph.price_usd <= p.take_profit_price THEN 'TAKE_PROFIT_TRIGGERED'
                    WHEN p.unlock_date <= NOW() + INTERVAL '1 day' THEN 'TIME_EXIT_SOON'
                    ELSE 'NORMAL'
                END as risk_status
            FROM positions p
            LEFT JOIN LATERAL (
                SELECT price_usd FROM price_history 
                WHERE token_address = p.token_address 
                ORDER BY timestamp DESC LIMIT 1
            ) ph ON true
            WHERE p.position_id = $1
        """
        
        return await self.db.execute_query(query, position_id, fetch='one')


class AsyncPriceCache:
    """
    High-performance price caching with batch operations
    """
    
    def __init__(self, db_pool: AsyncDatabasePool):
        self.db = db_pool
        self._price_cache = {}
        self._cache_timestamps = {}
    
    async def batch_update_prices(self, price_updates: List[Dict[str, Any]]) -> None:
        """Batch update prices for better performance"""
        if not price_updates:
            return
        
        # Prepare batch data
        batch_data = []
        current_time = datetime.now(timezone.utc)
        
        for update in price_updates:
            batch_data.append((
                update['token_address'].lower(),
                float(update['price']),
                current_time,
                update.get('source', 'unknown')
            ))
            
            # Update in-memory cache
            self._price_cache[update['token_address'].lower()] = float(update['price'])
            self._cache_timestamps[update['token_address'].lower()] = current_time
        
        # Batch insert/update
        query = """
            INSERT INTO price_history (token_address, price_usd, timestamp, source)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (token_address, timestamp) DO UPDATE SET
            price_usd = EXCLUDED.price_usd, source = EXCLUDED.source
        """
        
        await self.db.execute_batch(query, batch_data)
        logging.debug(f"✅ Batch updated {len(batch_data)} prices")
    
    async def get_cached_price(self, token_address: str) -> Optional[float]:
        """Get price from cache with fallback to database"""
        token_address = token_address.lower()
        
        # Check in-memory cache first
        if token_address in self._price_cache:
            cache_time = self._cache_timestamps.get(token_address)
            if cache_time and (datetime.now(timezone.utc) - cache_time).seconds < 300:  # 5 min cache
                return self._price_cache[token_address]
        
        # Fallback to database
        query = """
            SELECT price_usd FROM price_history
            WHERE token_address = $1
            ORDER BY timestamp DESC LIMIT 1
        """
        
        price = await self.db.execute_query(query, token_address, fetch='val')
        
        if price:
            self._price_cache[token_address] = float(price)
            self._cache_timestamps[token_address] = datetime.now(timezone.utc)
        
        return float(price) if price else None


# Global instances
db_pool: Optional[AsyncDatabasePool] = None
position_manager: Optional[AsyncPositionManager] = None
price_cache: Optional[AsyncPriceCache] = None


async def initialize_async_database(database_url: str):
    """Initialize the async database components"""
    global db_pool, position_manager, price_cache
    
    db_pool = AsyncDatabasePool(database_url)
    await db_pool.initialize()
    
    position_manager = AsyncPositionManager(db_pool)
    price_cache = AsyncPriceCache(db_pool)
    
    logging.info("✅ Async database components initialized")


async def cleanup_async_database():
    """Cleanup database connections"""
    global db_pool
    
    if db_pool:
        await db_pool.close()
    
    logging.info("✅ Async database cleanup complete")


# Export public interface
__all__ = [
    'AsyncDatabasePool',
    'AsyncPositionManager', 
    'AsyncPriceCache',
    'initialize_async_database',
    'cleanup_async_database',
    'db_pool',
    'position_manager',
    'price_cache'
]
