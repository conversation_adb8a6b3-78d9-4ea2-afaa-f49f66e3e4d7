"""
Tests for Secure Configuration Management
=======================================

Tests for:
- SecureConfigManager functionality
- Configuration validation
- Secret management
- Environment detection
- Security features
"""

import pytest
import os
from unittest.mock import patch, MagicMock
from decimal import Decimal

# Add common path for imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / 'services' / 'common'))

try:
    from secure_config import (
        Environment, ConfigurationError, SecureConfigManager,
        ConfigurationValidator, SecureConfigurationManager,
        get_secure_config, get_configuration_manager
    )
    from security import SecretManager, SecurityError
except ImportError as e:
    print(f"Warning: Could not import modules: {e}")
    
    # Mock implementations for testing
    class Environment:
        DEVELOPMENT = "development"
        TESTING = "testing"
        STAGING = "staging"
        PRODUCTION = "production"
    
    class ConfigurationError(Exception):
        pass
    
    class SecurityError(Exception):
        pass
    
    class SecretManager:
        def __init__(self, master_key=None):
            self.master_key = master_key
        
        def get_secret(self, key, default=None):
            return os.environ.get(key, default)
        
        def encrypt_secret(self, secret):
            return f"encrypted_{secret}"
        
        def decrypt_secret(self, encrypted):
            return encrypted.replace("encrypted_", "")
    
    class SecureConfigManager:
        def __init__(self):
            self.database = MagicMock()
            self.redis = MagicMock()
            self.api = MagicMock()
            self.trading = MagicMock()
            self.system = MagicMock()
        
        def validate_all_configurations(self):
            return {
                'database': {'valid': True, 'errors': []},
                'redis': {'valid': True, 'errors': []},
                'api': {'valid': True, 'errors': []},
                'trading': {'valid': True, 'errors': []},
                'system': {'valid': True, 'errors': []}
            }
        
        def get_configuration_summary(self):
            return {'environment': 'testing'}
    
    class ConfigurationValidator:
        @staticmethod
        def validate_environment_setup():
            return {'status': 'valid'}
    
    class SecureConfigurationManager:
        def __init__(self, environment=None):
            self.environment = environment or Environment.DEVELOPMENT
            self.config = SecureConfigManager()
            self.validator = ConfigurationValidator()
        
        def get_config(self):
            return self.config
        
        def health_check(self):
            return {'status': 'healthy'}
    
    def get_secure_config():
        return SecureConfigManager()
    
    def get_configuration_manager():
        return SecureConfigurationManager()


class TestSecretManager:
    """Test secret management functionality"""
    
    def test_secret_manager_initialization(self):
        """Test SecretManager initialization"""
        manager = SecretManager()
        assert manager is not None
    
    def test_secret_encryption_decryption(self):
        """Test secret encryption and decryption"""
        manager = SecretManager("test_master_key")
        
        original_secret = "my_secret_value"
        encrypted = manager.encrypt_secret(original_secret)
        decrypted = manager.decrypt_secret(encrypted)
        
        assert encrypted != original_secret
        assert decrypted == original_secret
    
    def test_get_secret_from_environment(self):
        """Test getting secrets from environment variables"""
        with patch.dict(os.environ, {'TEST_SECRET': 'test_value'}):
            manager = SecretManager()
            secret = manager.get_secret('TEST_SECRET')
            assert secret == 'test_value'
    
    def test_get_secret_not_found(self):
        """Test getting non-existent secret"""
        manager = SecretManager()
        secret = manager.get_secret('NON_EXISTENT_SECRET')
        assert secret is None
    
    def test_get_secret_with_default(self):
        """Test getting secret with default value"""
        manager = SecretManager()
        secret = manager.get_secret('NON_EXISTENT_SECRET', 'default_value')
        assert secret == 'default_value'


class TestConfigurationValidation:
    """Test configuration validation"""
    
    def test_environment_variable_validation(self):
        """Test environment variable validation"""
        validator = ConfigurationValidator()
        
        # Mock environment with required variables
        with patch.dict(os.environ, {
            'DATABASE_URL': 'postgresql://test:test@localhost:5432/test',
            'REDIS_URL': 'redis://localhost:6379',
            'INFURA_API_KEY': 'test_infura_key'
        }):
            result = validator.validate_environment_setup()
            
            assert 'environment_variables' in result
            env_vars = result['environment_variables']
            assert env_vars['status'] in ['valid', 'invalid']
    
    def test_missing_environment_variables(self):
        """Test validation with missing environment variables"""
        validator = ConfigurationValidator()
        
        # Clear environment
        with patch.dict(os.environ, {}, clear=True):
            result = validator.validate_environment_setup()
            
            env_vars = result['environment_variables']
            assert len(env_vars['missing_variables']) > 0
    
    def test_encryption_setup_validation(self):
        """Test encryption setup validation"""
        validator = ConfigurationValidator()
        
        result = validator.validate_environment_setup()
        
        assert 'encryption_setup' in result
        encryption = result['encryption_setup']
        assert 'status' in encryption


class TestSecureConfigManager:
    """Test SecureConfigManager functionality"""
    
    def test_config_manager_initialization(self):
        """Test configuration manager initialization"""
        with patch.dict(os.environ, {
            'DATABASE_URL': 'postgresql://test:test@localhost:5432/test',
            'REDIS_URL': 'redis://localhost:6379'
        }):
            config = SecureConfigManager()
            assert config is not None
    
    def test_configuration_validation(self):
        """Test configuration validation"""
        config = SecureConfigManager()
        
        validation_results = config.validate_all_configurations()
        
        assert isinstance(validation_results, dict)
        assert 'database' in validation_results
        assert 'redis' in validation_results
        assert 'api' in validation_results
        assert 'trading' in validation_results
        assert 'system' in validation_results
        
        # Each component should have validation status
        for component, result in validation_results.items():
            assert 'valid' in result
            assert 'errors' in result
            assert isinstance(result['valid'], bool)
            assert isinstance(result['errors'], list)
    
    def test_configuration_summary(self):
        """Test configuration summary generation"""
        config = SecureConfigManager()
        
        summary = config.get_configuration_summary()
        
        assert isinstance(summary, dict)
        # Should not contain sensitive data
        assert 'password' not in str(summary).lower()
        assert 'secret' not in str(summary).lower()
        assert 'key' not in str(summary).lower() or 'configured' in str(summary).lower()


class TestSecureConfigurationManager:
    """Test enhanced configuration manager"""
    
    def test_configuration_manager_initialization(self):
        """Test enhanced configuration manager initialization"""
        manager = SecureConfigurationManager()
        
        assert manager is not None
        assert hasattr(manager, 'environment')
        assert hasattr(manager, 'config')
        assert hasattr(manager, 'validator')
    
    def test_environment_detection(self):
        """Test environment detection"""
        # Test with explicit environment variable
        with patch.dict(os.environ, {'ENVIRONMENT': 'testing'}):
            manager = SecureConfigurationManager()
            assert manager.environment == Environment.TESTING
        
        # Test with default environment
        with patch.dict(os.environ, {}, clear=True):
            manager = SecureConfigurationManager()
            assert manager.environment == Environment.DEVELOPMENT
    
    def test_health_check(self):
        """Test health check functionality"""
        manager = SecureConfigurationManager()
        
        health_status = manager.health_check()
        
        assert isinstance(health_status, dict)
        assert 'configuration' in health_status
        assert 'environment' in health_status
        assert 'summary' in health_status
        assert 'timestamp' in health_status
    
    def test_get_config(self):
        """Test getting configuration from manager"""
        manager = SecureConfigurationManager()
        
        config = manager.get_config()
        
        assert config is not None
        assert isinstance(config, SecureConfigManager)


class TestGlobalConfigurationFunctions:
    """Test global configuration functions"""
    
    def test_get_secure_config(self):
        """Test global secure config function"""
        config = get_secure_config()
        
        assert config is not None
        assert isinstance(config, SecureConfigManager)
    
    def test_get_configuration_manager(self):
        """Test global configuration manager function"""
        manager = get_configuration_manager()
        
        assert manager is not None
        assert isinstance(manager, SecureConfigurationManager)
    
    def test_singleton_behavior(self):
        """Test that global functions return same instances"""
        config1 = get_secure_config()
        config2 = get_secure_config()
        
        # Should return same instance (singleton pattern)
        assert config1 is config2
        
        manager1 = get_configuration_manager()
        manager2 = get_configuration_manager()
        
        assert manager1 is manager2


class TestConfigurationSecurity:
    """Test security aspects of configuration"""
    
    def test_sensitive_data_not_logged(self):
        """Test that sensitive data is not exposed in logs"""
        with patch('logging.info') as mock_log:
            config = SecureConfigManager()
            summary = config.get_configuration_summary()
            
            # Check that summary doesn't contain sensitive data
            summary_str = str(summary)
            assert 'password' not in summary_str.lower()
            assert 'private_key' not in summary_str.lower()
            assert 'secret' not in summary_str.lower()
    
    def test_configuration_validation_errors(self):
        """Test configuration validation with invalid data"""
        # This would test actual validation errors in a real implementation
        config = SecureConfigManager()
        
        # Should not raise exceptions during validation
        try:
            validation_results = config.validate_all_configurations()
            assert isinstance(validation_results, dict)
        except Exception as e:
            pytest.fail(f"Configuration validation should not raise exceptions: {e}")


if __name__ == "__main__":
    # Run tests directly
    pytest.main([__file__, "-v"])
