"""
Oracle Database Handler - Updated to use Unified Database Manager
================================================================

This module has been updated to use the unified database manager,
eliminating duplicate database connection code.
"""

import logging
from typing import Dict, Any, List
from datetime import datetime, timedelta

# Import unified database operations
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'common'))

from database_manager import get_unified_db_operations
from unified_config import get_config
from unified_utils import LoggingUtils, DataValidationUtils

def init_database():
    """Initialize database tables if they don't exist - using unified database manager"""
    try:
        db_ops = get_unified_db_operations()

        # The unified database manager handles table creation through schema deployment
        # This function is kept for backward compatibility but delegates to unified operations
        LoggingUtils.log_operation_success("Database initialization", {"method": "unified_manager"})

        return True

    except Exception as e:
        LoggingUtils.log_operation_failure("Database initialization", e)
        raise
            
            # Create positions table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS positions (
                    id SERIAL PRIMARY KEY,
                    token_symbol VARCHAR(20) NOT NULL,
                    token_address VARCHAR(42) NOT NULL,
                    amount_shorted DECIMAL NOT NULL,
                    entry_price_in_usdc DECIMAL NOT NULL,
                    unlock_date TIMESTAMP WITH TIME ZONE NOT NULL,
                    status VARCHAR(20) DEFAULT 'OPEN',
                    borrow_tx_hash VARCHAR(66),
                    swap_tx_hash VARCHAR(66),
                    close_tx_hash VARCHAR(66),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    closed_at TIMESTAMP WITH TIME ZONE
                )
            """)
            
            # Create indexes
            cur.execute("CREATE INDEX IF NOT EXISTS idx_unlock_events_date ON unlock_events(unlock_date)")
            cur.execute("CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status)")
            cur.execute("CREATE INDEX IF NOT EXISTS idx_positions_token ON positions(token_address)")
            
            conn.commit()
            logging.info("Database tables initialized successfully")
    except Exception as e:
        conn.rollback()
        logging.error(f"Error initializing database: {e}")
        raise
    finally:
        conn.close()

def store_unlock_events(events: List[Dict[str, Any]]):
    """Stores a list of unlock events using unified database operations"""
    if not events:
        LoggingUtils.log_operation_success("Store unlock events", {"count": 0, "reason": "no_events"})
        return

    try:
        # Validate events before storing
        valid_events = []
        for event in events:
            if DataValidationUtils.validate_unlock_event(event):
                valid_events.append(event)
            else:
                logging.warning(f"Skipping invalid event: {event.get('token_symbol', 'unknown')}")

        if not valid_events:
            LoggingUtils.log_operation_success("Store unlock events", {"count": 0, "reason": "no_valid_events"})
            return

        # Use unified database operations
        db_ops = get_unified_db_operations()
        stored_count = db_ops.store_unlock_events(valid_events)

        LoggingUtils.log_operation_success("Store unlock events", {
            "total_events": len(events),
            "valid_events": len(valid_events),
            "stored_count": stored_count
        })

    except Exception as e:
        LoggingUtils.log_operation_failure("Store unlock events", e, {"event_count": len(events)})
        raise

def get_upcoming_unlocks(days_ahead: int) -> List[Dict[str, Any]]:
    """Retrieves unlock events using unified database operations"""
    try:
        # Use unified database operations
        db_ops = get_unified_db_operations()
        results = db_ops.get_upcoming_unlocks(days_ahead)

        LoggingUtils.log_operation_success("Get upcoming unlocks", {
            "days_ahead": days_ahead,
            "results_count": len(results)
        })

        return results

    except Exception as e:
        LoggingUtils.log_operation_failure("Get upcoming unlocks", e, {"days_ahead": days_ahead})
        return []

def get_open_positions() -> List[Dict[str, Any]]:
    """Retrieves all open trading positions"""
    conn = _get_db_conn()
    try:
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("""
                SELECT id as position_id, token_symbol, token_address, 
                       amount_shorted, entry_price_in_usdc, unlock_date, status
                FROM positions 
                WHERE status = 'OPEN'
                ORDER BY created_at ASC
            """)
            
            results = cur.fetchall()
            return [dict(row) for row in results]
    except Exception as e:
        logging.error(f"Error querying open positions: {e}")
        return []
    finally:
        conn.close()

def log_trade_entry(token_symbol: str, token_address: str, amount_shorted: float, 
                   entry_price: float, unlock_date: str, borrow_tx_hash: str = None, 
                   swap_tx_hash: str = None) -> int:
    """Log a new trade position to the database"""
    conn = _get_db_conn()
    try:
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO positions 
                (token_symbol, token_address, amount_shorted, entry_price_in_usdc, 
                 unlock_date, borrow_tx_hash, swap_tx_hash)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (token_symbol, token_address, amount_shorted, entry_price, 
                  unlock_date, borrow_tx_hash, swap_tx_hash))
            
            position_id = cur.fetchone()[0]
            conn.commit()
            logging.info(f"Logged new position with ID: {position_id}")
            return position_id
    except Exception as e:
        conn.rollback()
        logging.error(f"Error logging trade entry: {e}")
        raise
    finally:
        conn.close()

def update_position_status(position_id: int, status: str, close_tx_hash: str = None):
    """Update position status"""
    conn = _get_db_conn()
    try:
        with conn.cursor() as cur:
            if status == 'CLOSED':
                cur.execute("""
                    UPDATE positions 
                    SET status = %s, close_tx_hash = %s, closed_at = NOW()
                    WHERE id = %s
                """, (status, close_tx_hash, position_id))
            else:
                cur.execute("""
                    UPDATE positions 
                    SET status = %s
                    WHERE id = %s
                """, (status, position_id))
            
            conn.commit()
            logging.info(f"Updated position {position_id} status to {status}")
    except Exception as e:
        conn.rollback()
        logging.error(f"Error updating position status: {e}")
        raise
    finally:
        conn.close()
