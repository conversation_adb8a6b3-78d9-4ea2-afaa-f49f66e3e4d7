"""
Comprehensive Tests for Async Database Operations
===============================================

Tests for:
- AsyncDatabasePool connection management
- AsyncPositionManager operations
- AsyncPriceCache performance
- Error handling and retry logic
- Connection pooling behavior
- Batch operations
"""

import pytest
import asyncio
import asyncpg
import os
from unittest.mock import AsyncMock, MagicMock, patch
from decimal import Decimal
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List

# Add common path for imports
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / 'services' / 'common'))

try:
    from async_database import (
        AsyncDatabasePool, AsyncPositionManager, AsyncPriceCache,
        initialize_async_database, cleanup_async_database
    )
    from models import Position, PositionStatus
    from error_handling import ChimeraError
    from constants import DatabaseConstants
except ImportError as e:
    # Mock the imports for testing when modules aren't available
    print(f"Warning: Could not import modules: {e}")

    class MockDatabaseConstants:
        MIN_POOL_SIZE = 5
        MAX_POOL_SIZE = 20
        DEFAULT_QUERY_TIMEOUT = 30

    DatabaseConstants = MockDatabaseConstants

    class ChimeraError(Exception):
        pass

    class AsyncDatabasePool:
        def __init__(self, url): pass
        async def initialize(self): pass
        async def execute_query(self, *args, **kwargs): pass
        async def execute_batch(self, *args, **kwargs): pass
        async def close(self): pass

    class AsyncPositionManager:
        def __init__(self, pool): pass
        async def create_position(self, data): return 123
        async def get_open_positions(self): return []
        async def update_position_price(self, id, price): pass
        async def close_position(self, **kwargs): pass

    class AsyncPriceCache:
        def __init__(self, pool): pass
        async def cache_price(self, addr, price, source): pass
        async def get_latest_price(self, addr): return None

    async def initialize_async_database(url): pass
    async def cleanup_async_database(): pass


class TestAsyncDatabasePool:
    """Test suite for AsyncDatabasePool"""
    
    @pytest.fixture
    async def mock_pool(self):
        """Create a mock database pool"""
        pool = AsyncDatabasePool("postgresql://test:test@localhost:5432/test_db")
        
        # Mock the asyncpg pool
        mock_pg_pool = AsyncMock()
        pool.pool = mock_pg_pool
        
        # Mock connection
        mock_conn = AsyncMock()
        mock_pg_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_pg_pool.acquire.return_value.__aexit__.return_value = None
        
        return pool, mock_conn
    
    @pytest.mark.asyncio
    async def test_pool_initialization(self):
        """Test database pool initialization"""
        with patch('asyncpg.create_pool') as mock_create_pool:
            mock_pool = AsyncMock()
            mock_create_pool.return_value = mock_pool
            
            db_pool = AsyncDatabasePool("postgresql://test:test@localhost:5432/test_db")
            await db_pool.initialize()
            
            # Verify pool creation with correct parameters
            mock_create_pool.assert_called_once()
            call_args = mock_create_pool.call_args
            
            assert call_args[0][0] == "postgresql://test:test@localhost:5432/test_db"
            assert call_args[1]['min_size'] == DatabaseConstants.MIN_POOL_SIZE
            assert call_args[1]['max_size'] == DatabaseConstants.MAX_POOL_SIZE
            assert 'application_name' in call_args[1]['server_settings']
    
    @pytest.mark.asyncio
    async def test_execute_query_fetch_all(self, mock_pool):
        """Test execute_query with fetch='all'"""
        pool, mock_conn = mock_pool
        
        # Mock query result
        mock_result = [
            {'id': 1, 'name': 'test1'},
            {'id': 2, 'name': 'test2'}
        ]
        mock_conn.fetch.return_value = [
            MagicMock(**row) for row in mock_result
        ]
        
        result = await pool.execute_query(
            "SELECT * FROM test_table", 
            fetch='all'
        )
        
        assert len(result) == 2
        assert result[0]['id'] == 1
        assert result[1]['name'] == 'test2'
        mock_conn.fetch.assert_called_once_with("SELECT * FROM test_table")
    
    @pytest.mark.asyncio
    async def test_execute_query_fetch_one(self, mock_pool):
        """Test execute_query with fetch='one'"""
        pool, mock_conn = mock_pool
        
        # Mock single row result
        mock_row = MagicMock()
        mock_row.__iter__ = lambda self: iter([('id', 1), ('name', 'test')])
        mock_conn.fetchrow.return_value = mock_row
        
        result = await pool.execute_query(
            "SELECT * FROM test_table WHERE id = $1", 
            1,
            fetch='one'
        )
        
        assert result is not None
        mock_conn.fetchrow.assert_called_once_with(
            "SELECT * FROM test_table WHERE id = $1", 1
        )
    
    @pytest.mark.asyncio
    async def test_execute_query_error_handling(self, mock_pool):
        """Test error handling in execute_query"""
        pool, mock_conn = mock_pool
        
        # Mock database error
        mock_conn.fetch.side_effect = asyncpg.PostgresError("Connection failed")
        
        with pytest.raises(ChimeraError) as exc_info:
            await pool.execute_query("SELECT * FROM test_table", fetch='all')
        
        assert "Database query failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_execute_batch(self, mock_pool):
        """Test batch operations"""
        pool, mock_conn = mock_pool
        
        batch_data = [
            (1, 'test1'),
            (2, 'test2'),
            (3, 'test3')
        ]
        
        await pool.execute_batch(
            "INSERT INTO test_table (id, name) VALUES ($1, $2)",
            batch_data
        )
        
        mock_conn.executemany.assert_called_once_with(
            "INSERT INTO test_table (id, name) VALUES ($1, $2)",
            batch_data
        )
    
    @pytest.mark.asyncio
    async def test_batch_empty_list(self, mock_pool):
        """Test batch operation with empty list"""
        pool, mock_conn = mock_pool
        
        # Should not call executemany for empty list
        await pool.execute_batch("INSERT INTO test_table VALUES ($1)", [])
        
        mock_conn.executemany.assert_not_called()


class TestAsyncPositionManager:
    """Test suite for AsyncPositionManager"""
    
    @pytest.fixture
    async def position_manager(self):
        """Create position manager with mocked database"""
        mock_pool = AsyncMock()
        manager = AsyncPositionManager(mock_pool)
        return manager, mock_pool
    
    @pytest.mark.asyncio
    async def test_create_position(self, position_manager):
        """Test creating a new position"""
        manager, mock_pool = position_manager
        
        # Mock successful insert
        mock_pool.execute_query.return_value = 123
        
        position_data = {
            'token_symbol': 'ETH',
            'token_address': '0x' + '0' * 40,
            'amount_shorted': Decimal('10.5'),
            'entry_price_in_usdc': Decimal('2000.0'),
            'unlock_date': datetime.now(timezone.utc) + timedelta(days=7),
            'strategy_id': 'test_strategy'
        }
        
        position_id = await manager.create_position(position_data)
        
        assert position_id == 123
        mock_pool.execute_query.assert_called_once()
        
        # Verify the query parameters
        call_args = mock_pool.execute_query.call_args
        assert 'INSERT INTO positions' in call_args[0][0]
        assert call_args[1]['fetch'] == 'val'
    
    @pytest.mark.asyncio
    async def test_get_open_positions(self, position_manager):
        """Test retrieving open positions"""
        manager, mock_pool = position_manager
        
        # Mock query result
        mock_positions = [
            {
                'position_id': 1,
                'token_symbol': 'ETH',
                'token_address': '0x' + '0' * 40,
                'amount_shorted': Decimal('10.0'),
                'entry_price_in_usdc': Decimal('2000.0'),
                'status': 'OPEN'
            },
            {
                'position_id': 2,
                'token_symbol': 'BTC',
                'token_address': '0x' + '1' * 40,
                'amount_shorted': Decimal('0.5'),
                'entry_price_in_usdc': Decimal('50000.0'),
                'status': 'OPEN'
            }
        ]
        mock_pool.execute_query.return_value = mock_positions
        
        positions = await manager.get_open_positions()
        
        assert len(positions) == 2
        assert positions[0]['token_symbol'] == 'ETH'
        assert positions[1]['token_symbol'] == 'BTC'
        
        # Verify query
        call_args = mock_pool.execute_query.call_args
        assert 'SELECT' in call_args[0][0]
        assert "status = 'OPEN'" in call_args[0][0]
        assert call_args[1]['fetch'] == 'all'
    
    @pytest.mark.asyncio
    async def test_update_position_price(self, position_manager):
        """Test updating position current price"""
        manager, mock_pool = position_manager
        
        await manager.update_position_price(123, Decimal('1950.0'))
        
        mock_pool.execute_query.assert_called_once()
        call_args = mock_pool.execute_query.call_args
        
        assert 'UPDATE positions' in call_args[0][0]
        assert 'current_price' in call_args[0][0]
        assert call_args[0][1] == Decimal('1950.0')
        assert call_args[0][2] == 123
    
    @pytest.mark.asyncio
    async def test_close_position(self, position_manager):
        """Test closing a position"""
        manager, mock_pool = position_manager
        
        await manager.close_position(
            position_id=123,
            close_price=Decimal('1800.0'),
            pnl_usd=Decimal('200.0'),
            close_reason="Take profit hit"
        )
        
        mock_pool.execute_query.assert_called_once()
        call_args = mock_pool.execute_query.call_args
        
        assert 'UPDATE positions' in call_args[0][0]
        assert 'status' in call_args[0][0]
        assert 'close_price' in call_args[0][0]
        assert 'pnl_usd' in call_args[0][0]
        assert call_args[0][1] == 'CLOSED'


class TestAsyncPriceCache:
    """Test suite for AsyncPriceCache"""
    
    @pytest.fixture
    async def price_cache(self):
        """Create price cache with mocked database"""
        mock_pool = AsyncMock()
        cache = AsyncPriceCache(mock_pool)
        return cache, mock_pool
    
    @pytest.mark.asyncio
    async def test_cache_price(self, price_cache):
        """Test caching a price"""
        cache, mock_pool = price_cache
        
        token_address = '0x' + '0' * 40
        price = Decimal('2000.0')
        
        await cache.cache_price(token_address, price, 'binance')
        
        mock_pool.execute_query.assert_called_once()
        call_args = mock_pool.execute_query.call_args
        
        assert 'INSERT INTO price_history' in call_args[0][0]
        assert call_args[0][1] == token_address
        assert call_args[0][2] == price
        assert call_args[0][3] == 'binance'
    
    @pytest.mark.asyncio
    async def test_get_latest_price(self, price_cache):
        """Test getting latest price from cache"""
        cache, mock_pool = price_cache
        
        token_address = '0x' + '0' * 40
        mock_pool.execute_query.return_value = Decimal('2000.0')
        
        price = await cache.get_latest_price(token_address)
        
        assert price == 2000.0
        mock_pool.execute_query.assert_called_once()
        
        call_args = mock_pool.execute_query.call_args
        assert 'SELECT price_usd' in call_args[0][0]
        assert 'ORDER BY timestamp DESC' in call_args[0][0]
        assert 'LIMIT 1' in call_args[0][0]
        assert call_args[0][1] == token_address
        assert call_args[1]['fetch'] == 'val'
    
    @pytest.mark.asyncio
    async def test_get_latest_price_not_found(self, price_cache):
        """Test getting latest price when not found"""
        cache, mock_pool = price_cache

        token_address = '0x' + '0' * 40
        mock_pool.execute_query.return_value = None

        price = await cache.get_latest_price(token_address)

        assert price is None


class TestAsyncDatabaseIntegration:
    """Integration tests for async database components"""

    @pytest.mark.asyncio
    async def test_connection_pool_exhaustion(self):
        """Test behavior when connection pool is exhausted"""
        with patch('asyncpg.create_pool') as mock_create_pool:
            # Mock pool that raises exception when exhausted
            mock_pool = AsyncMock()
            mock_pool.acquire.side_effect = asyncpg.TooManyConnectionsError("Pool exhausted")
            mock_create_pool.return_value = mock_pool

            db_pool = AsyncDatabasePool("postgresql://test:test@localhost:5432/test_db")
            await db_pool.initialize()

            with pytest.raises(ChimeraError):
                await db_pool.execute_query("SELECT 1", fetch='val')

    @pytest.mark.asyncio
    async def test_retry_mechanism(self):
        """Test retry mechanism for transient failures"""
        with patch('asyncpg.create_pool') as mock_create_pool:
            mock_pool = AsyncMock()
            mock_conn = AsyncMock()

            # First call fails, second succeeds
            mock_conn.fetchval.side_effect = [
                asyncpg.ConnectionDoesNotExistError("Connection lost"),
                42
            ]

            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_pool.acquire.return_value.__aexit__.return_value = None
            mock_create_pool.return_value = mock_pool

            db_pool = AsyncDatabasePool("postgresql://test:test@localhost:5432/test_db")
            await db_pool.initialize()

            # Should succeed after retry
            result = await db_pool.execute_query("SELECT 1", fetch='val')
            assert result == 42

            # Verify retry happened
            assert mock_conn.fetchval.call_count == 2

    @pytest.mark.asyncio
    async def test_concurrent_operations(self):
        """Test concurrent database operations"""
        with patch('asyncpg.create_pool') as mock_create_pool:
            mock_pool = AsyncMock()
            mock_conn = AsyncMock()
            mock_conn.fetchval.return_value = 1

            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_pool.acquire.return_value.__aexit__.return_value = None
            mock_create_pool.return_value = mock_pool

            db_pool = AsyncDatabasePool("postgresql://test:test@localhost:5432/test_db")
            await db_pool.initialize()

            # Run multiple concurrent queries
            tasks = [
                db_pool.execute_query("SELECT 1", fetch='val')
                for _ in range(10)
            ]

            results = await asyncio.gather(*tasks)

            # All should succeed
            assert all(result == 1 for result in results)
            assert mock_conn.fetchval.call_count == 10

    @pytest.mark.asyncio
    async def test_transaction_rollback(self):
        """Test transaction rollback on error"""
        with patch('asyncpg.create_pool') as mock_create_pool:
            mock_pool = AsyncMock()
            mock_conn = AsyncMock()

            # Mock transaction context
            mock_transaction = AsyncMock()
            mock_conn.transaction.return_value = mock_transaction
            mock_transaction.__aenter__.return_value = mock_transaction
            mock_transaction.__aexit__.return_value = None

            # Simulate error during transaction
            mock_conn.execute.side_effect = asyncpg.PostgresError("Constraint violation")

            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_pool.acquire.return_value.__aexit__.return_value = None
            mock_create_pool.return_value = mock_pool

            db_pool = AsyncDatabasePool("postgresql://test:test@localhost:5432/test_db")
            await db_pool.initialize()

            # Transaction should rollback on error
            with pytest.raises(ChimeraError):
                async with db_pool.acquire_connection() as conn:
                    async with conn.transaction():
                        await conn.execute("INSERT INTO test VALUES (1)")
                        await conn.execute("INSERT INTO test VALUES (1)")  # Duplicate key error

    @pytest.mark.asyncio
    async def test_performance_monitoring(self):
        """Test performance monitoring and metrics"""
        with patch('asyncpg.create_pool') as mock_create_pool:
            mock_pool = AsyncMock()
            mock_conn = AsyncMock()
            mock_conn.fetchval.return_value = 1

            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_pool.acquire.return_value.__aexit__.return_value = None
            mock_create_pool.return_value = mock_pool

            db_pool = AsyncDatabasePool("postgresql://test:test@localhost:5432/test_db")
            await db_pool.initialize()

            # Execute some queries
            for _ in range(5):
                await db_pool.execute_query("SELECT 1", fetch='val')

            # Check metrics (if implemented)
            # This would test actual metrics collection in a real implementation
            assert mock_conn.fetchval.call_count == 5


class TestAsyncDatabasePerformance:
    """Performance tests for async database operations"""

    @pytest.mark.asyncio
    async def test_batch_insert_performance(self):
        """Test batch insert performance vs individual inserts"""
        with patch('asyncpg.create_pool') as mock_create_pool:
            mock_pool = AsyncMock()
            mock_conn = AsyncMock()

            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_pool.acquire.return_value.__aexit__.return_value = None
            mock_create_pool.return_value = mock_pool

            db_pool = AsyncDatabasePool("postgresql://test:test@localhost:5432/test_db")
            await db_pool.initialize()

            # Test batch operation
            batch_data = [(i, f'test{i}') for i in range(100)]

            import time
            start_time = time.time()
            await db_pool.execute_batch(
                "INSERT INTO test (id, name) VALUES ($1, $2)",
                batch_data
            )
            batch_time = time.time() - start_time

            # Verify executemany was called once with all data
            mock_conn.executemany.assert_called_once_with(
                "INSERT INTO test (id, name) VALUES ($1, $2)",
                batch_data
            )

            # Batch should be more efficient than individual operations
            assert batch_time < 1.0  # Should be very fast with mocking

    @pytest.mark.asyncio
    async def test_connection_reuse(self):
        """Test connection reuse in pool"""
        with patch('asyncpg.create_pool') as mock_create_pool:
            mock_pool = AsyncMock()
            mock_conn = AsyncMock()
            mock_conn.fetchval.return_value = 1

            # Track connection acquisitions
            acquire_count = 0
            def track_acquire():
                nonlocal acquire_count
                acquire_count += 1
                return mock_conn

            mock_pool.acquire.return_value.__aenter__.side_effect = track_acquire
            mock_pool.acquire.return_value.__aexit__.return_value = None
            mock_create_pool.return_value = mock_pool

            db_pool = AsyncDatabasePool("postgresql://test:test@localhost:5432/test_db")
            await db_pool.initialize()

            # Execute multiple queries
            for _ in range(5):
                await db_pool.execute_query("SELECT 1", fetch='val')

            # Should reuse connections from pool
            assert acquire_count == 5  # One acquisition per query

    @pytest.mark.asyncio
    async def test_prepared_statement_usage(self):
        """Test prepared statement usage for performance"""
        with patch('asyncpg.create_pool') as mock_create_pool:
            mock_pool = AsyncMock()
            mock_conn = AsyncMock()

            # Mock prepared statement
            mock_prepared = AsyncMock()
            mock_conn.prepare.return_value = mock_prepared
            mock_prepared.fetchval.return_value = 1

            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_pool.acquire.return_value.__aexit__.return_value = None
            mock_create_pool.return_value = mock_pool

            db_pool = AsyncDatabasePool("postgresql://test:test@localhost:5432/test_db")
            await db_pool.initialize()

            # This would test prepared statement usage in a real implementation
            # For now, just verify the pool was initialized
            assert db_pool.pool is not None


@pytest.mark.asyncio
async def test_initialize_async_database():
    """Test initialization of global database components"""
    with patch('asyncpg.create_pool') as mock_create_pool:
        mock_pool = AsyncMock()
        mock_create_pool.return_value = mock_pool

        # Test initialization
        await initialize_async_database("postgresql://test:test@localhost:5432/test_db")

        # Verify components are initialized
        from async_database import db_pool, position_manager, price_cache

        assert db_pool is not None
        assert position_manager is not None
        assert price_cache is not None


@pytest.mark.asyncio
async def test_cleanup_async_database():
    """Test cleanup of database connections"""
    with patch('asyncpg.create_pool') as mock_create_pool:
        mock_pool = AsyncMock()
        mock_create_pool.return_value = mock_pool

        # Initialize first
        await initialize_async_database("postgresql://test:test@localhost:5432/test_db")

        # Then cleanup
        await cleanup_async_database()

        # Verify pool close was called
        mock_pool.close.assert_called_once()
