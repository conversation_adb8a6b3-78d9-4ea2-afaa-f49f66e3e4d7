"""
Deploy Optimized Database Schema
==============================

Script to deploy the optimized database schema with:
- Partitioned tables for performance
- Composite indexes for query optimization
- Materialized views for analytics
- Performance monitoring setup
- Automated maintenance functions
"""

import os
import sys
import logging
import asyncio
import asyncpg
from pathlib import Path
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

# Add common path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / 'services' / 'common'))

try:
    from secure_config import get_secure_config
    from error_handling import ChimeraError, log_error_with_context
except ImportError as e:
    print(f"Warning: Could not import modules: {e}")
    
    class ChimeraError(Exception):
        pass
    
    def log_error_with_context(error, context=None):
        logging.error(f"Error: {error}, Context: {context}")
    
    def get_secure_config():
        class MockConfig:
            class database:
                url = os.environ.get('DATABASE_URL', 'postgresql://postgres:password@localhost:5432/chimera_db')
        return MockConfig()

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


class DatabaseSchemaDeployer:
    """Deploy and manage optimized database schema"""
    
    def __init__(self, database_url: Optional[str] = None):
        self.database_url = database_url or self._get_database_url()
        self.connection: Optional[asyncpg.Connection] = None
        self.deployment_log: List[Dict[str, Any]] = []
        
    def _get_database_url(self) -> str:
        """Get database URL from configuration"""
        try:
            config = get_secure_config()
            return config.database.url
        except Exception:
            # Fallback to environment variable
            url = os.environ.get('DATABASE_URL')
            if not url:
                raise ChimeraError("DATABASE_URL not found in configuration or environment")
            return url
    
    async def connect(self):
        """Connect to the database"""
        try:
            self.connection = await asyncpg.connect(self.database_url)
            logging.info("✅ Connected to database successfully")
            
            # Log connection info (without sensitive data)
            server_version = await self.connection.fetchval('SELECT version()')
            logging.info(f"📊 Database version: {server_version.split(',')[0]}")
            
        except Exception as e:
            log_error_with_context(e, context={'operation': 'database_connection'})
            raise ChimeraError(f"Failed to connect to database: {str(e)}")
    
    async def disconnect(self):
        """Disconnect from the database"""
        if self.connection:
            await self.connection.close()
            logging.info("✅ Disconnected from database")
    
    async def check_existing_schema(self) -> Dict[str, Any]:
        """Check existing database schema"""
        if not self.connection:
            raise ChimeraError("Not connected to database")
        
        schema_info = {
            'tables': [],
            'indexes': [],
            'materialized_views': [],
            'functions': []
        }
        
        try:
            # Check existing tables
            tables = await self.connection.fetch("""
                SELECT table_name, table_type 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
            """)
            schema_info['tables'] = [dict(row) for row in tables]
            
            # Check existing indexes
            indexes = await self.connection.fetch("""
                SELECT indexname, tablename 
                FROM pg_indexes 
                WHERE schemaname = 'public'
            """)
            schema_info['indexes'] = [dict(row) for row in indexes]
            
            # Check materialized views
            matviews = await self.connection.fetch("""
                SELECT matviewname 
                FROM pg_matviews 
                WHERE schemaname = 'public'
            """)
            schema_info['materialized_views'] = [row['matviewname'] for row in matviews]
            
            # Check functions
            functions = await self.connection.fetch("""
                SELECT routine_name 
                FROM information_schema.routines 
                WHERE routine_schema = 'public' AND routine_type = 'FUNCTION'
            """)
            schema_info['functions'] = [row['routine_name'] for row in functions]
            
            logging.info(f"📊 Existing schema: {len(schema_info['tables'])} tables, "
                        f"{len(schema_info['indexes'])} indexes, "
                        f"{len(schema_info['materialized_views'])} materialized views")
            
            return schema_info
            
        except Exception as e:
            log_error_with_context(e, context={'operation': 'schema_check'})
            raise ChimeraError(f"Failed to check existing schema: {str(e)}")
    
    async def backup_existing_data(self) -> bool:
        """Create backup of existing data"""
        if not self.connection:
            raise ChimeraError("Not connected to database")
        
        try:
            # Check if critical tables exist and have data
            critical_tables = ['positions', 'unlock_events', 'price_history']
            backup_needed = False
            
            for table in critical_tables:
                try:
                    count = await self.connection.fetchval(f"SELECT COUNT(*) FROM {table}")
                    if count > 0:
                        logging.info(f"📊 Table {table} has {count} rows - backup recommended")
                        backup_needed = True
                except Exception:
                    # Table doesn't exist, no backup needed
                    pass
            
            if backup_needed:
                logging.warning("⚠️ Existing data detected. Consider creating a backup before deployment.")
                logging.info("💡 Use pg_dump to create a backup: pg_dump -h host -U user -d database > backup.sql")
            
            return backup_needed
            
        except Exception as e:
            log_error_with_context(e, context={'operation': 'backup_check'})
            return False
    
    async def deploy_schema(self, force: bool = False) -> bool:
        """Deploy the optimized schema"""
        if not self.connection:
            raise ChimeraError("Not connected to database")
        
        try:
            # Read the optimized schema file
            schema_file = Path(__file__).parent.parent / 'database' / 'optimized_schema.sql'
            
            if not schema_file.exists():
                raise ChimeraError(f"Schema file not found: {schema_file}")
            
            schema_sql = schema_file.read_text(encoding='utf-8')
            logging.info(f"📄 Loaded schema file: {len(schema_sql)} characters")
            
            # Check for existing data
            backup_needed = await self.backup_existing_data()
            if backup_needed and not force:
                logging.error("❌ Existing data detected. Use --force to proceed or create a backup first.")
                return False
            
            # Start deployment
            logging.info("🚀 Starting schema deployment...")
            start_time = datetime.now(timezone.utc)
            
            # Execute schema in a transaction
            async with self.connection.transaction():
                await self.connection.execute(schema_sql)
            
            end_time = datetime.now(timezone.utc)
            duration = (end_time - start_time).total_seconds()
            
            logging.info(f"✅ Schema deployed successfully in {duration:.2f} seconds")
            
            # Log deployment
            self.deployment_log.append({
                'timestamp': end_time.isoformat(),
                'operation': 'schema_deployment',
                'duration_seconds': duration,
                'status': 'success'
            })
            
            return True
            
        except Exception as e:
            log_error_with_context(e, context={'operation': 'schema_deployment'})
            
            # Log failed deployment
            self.deployment_log.append({
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'operation': 'schema_deployment',
                'status': 'failed',
                'error': str(e)
            })
            
            raise ChimeraError(f"Failed to deploy schema: {str(e)}")
    
    async def verify_deployment(self) -> Dict[str, Any]:
        """Verify the deployment was successful"""
        if not self.connection:
            raise ChimeraError("Not connected to database")
        
        verification_results = {
            'tables_created': [],
            'indexes_created': [],
            'materialized_views_created': [],
            'functions_created': [],
            'partitions_created': [],
            'validation_passed': False
        }
        
        try:
            # Check expected tables
            expected_tables = [
                'unlock_events', 'positions', 'price_history', 
                'risk_alerts', 'system_logs'
            ]
            
            for table in expected_tables:
                exists = await self.connection.fetchval("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.tables 
                        WHERE table_schema = 'public' AND table_name = $1
                    )
                """, table)
                
                if exists:
                    verification_results['tables_created'].append(table)
                    logging.info(f"✅ Table verified: {table}")
                else:
                    logging.error(f"❌ Table missing: {table}")
            
            # Check expected indexes
            expected_indexes = [
                'idx_unlock_events_date_score',
                'idx_positions_status_token',
                'idx_price_history_token_time'
            ]
            
            for index in expected_indexes:
                exists = await self.connection.fetchval("""
                    SELECT EXISTS (
                        SELECT 1 FROM pg_indexes 
                        WHERE schemaname = 'public' AND indexname = $1
                    )
                """, index)
                
                if exists:
                    verification_results['indexes_created'].append(index)
                    logging.info(f"✅ Index verified: {index}")
                else:
                    logging.warning(f"⚠️ Index missing: {index}")
            
            # Check materialized views
            expected_matviews = [
                'portfolio_summary', 'token_performance', 'recent_price_movements'
            ]
            
            for matview in expected_matviews:
                exists = await self.connection.fetchval("""
                    SELECT EXISTS (
                        SELECT 1 FROM pg_matviews 
                        WHERE schemaname = 'public' AND matviewname = $1
                    )
                """, matview)
                
                if exists:
                    verification_results['materialized_views_created'].append(matview)
                    logging.info(f"✅ Materialized view verified: {matview}")
                else:
                    logging.warning(f"⚠️ Materialized view missing: {matview}")
            
            # Check functions
            expected_functions = [
                'refresh_materialized_views', 'cleanup_old_price_data', 'update_table_statistics'
            ]
            
            for function in expected_functions:
                exists = await self.connection.fetchval("""
                    SELECT EXISTS (
                        SELECT 1 FROM information_schema.routines 
                        WHERE routine_schema = 'public' AND routine_name = $1
                    )
                """, function)
                
                if exists:
                    verification_results['functions_created'].append(function)
                    logging.info(f"✅ Function verified: {function}")
                else:
                    logging.warning(f"⚠️ Function missing: {function}")
            
            # Check partitions for price_history
            partitions = await self.connection.fetch("""
                SELECT schemaname, tablename 
                FROM pg_tables 
                WHERE schemaname = 'public' AND tablename LIKE 'price_history_%'
            """)
            
            verification_results['partitions_created'] = [row['tablename'] for row in partitions]
            logging.info(f"✅ Found {len(partitions)} price_history partitions")
            
            # Overall validation
            tables_ok = len(verification_results['tables_created']) >= 4
            indexes_ok = len(verification_results['indexes_created']) >= 2
            matviews_ok = len(verification_results['materialized_views_created']) >= 2
            
            verification_results['validation_passed'] = tables_ok and indexes_ok and matviews_ok
            
            if verification_results['validation_passed']:
                logging.info("✅ Deployment verification PASSED")
            else:
                logging.error("❌ Deployment verification FAILED")
            
            return verification_results
            
        except Exception as e:
            log_error_with_context(e, context={'operation': 'deployment_verification'})
            raise ChimeraError(f"Failed to verify deployment: {str(e)}")
    
    async def setup_maintenance_jobs(self):
        """Set up automated maintenance jobs"""
        if not self.connection:
            raise ChimeraError("Not connected to database")
        
        try:
            logging.info("🔧 Setting up maintenance jobs...")
            
            # Test maintenance functions
            await self.connection.execute("SELECT refresh_materialized_views()")
            logging.info("✅ Materialized views refreshed")
            
            await self.connection.execute("SELECT update_table_statistics()")
            logging.info("✅ Table statistics updated")
            
            # Note: In production, you would set up cron jobs or use pg_cron extension
            logging.info("💡 Set up cron jobs for automated maintenance:")
            logging.info("   - Refresh materialized views: every 15 minutes")
            logging.info("   - Update statistics: daily")
            logging.info("   - Cleanup old data: weekly")
            
        except Exception as e:
            log_error_with_context(e, context={'operation': 'maintenance_setup'})
            logging.warning(f"⚠️ Maintenance setup failed: {str(e)}")
    
    def get_deployment_summary(self) -> Dict[str, Any]:
        """Get deployment summary"""
        return {
            'deployment_log': self.deployment_log,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'database_url_configured': bool(self.database_url)
        }


async def main():
    """Main deployment function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Deploy optimized database schema')
    parser.add_argument('--force', action='store_true', 
                       help='Force deployment even if existing data is detected')
    parser.add_argument('--verify-only', action='store_true',
                       help='Only verify existing deployment')
    parser.add_argument('--database-url', type=str,
                       help='Database URL (overrides configuration)')
    
    args = parser.parse_args()
    
    logging.info("🚀 Starting optimized database schema deployment")
    
    deployer = DatabaseSchemaDeployer(args.database_url)
    
    try:
        # Connect to database
        await deployer.connect()
        
        # Check existing schema
        existing_schema = await deployer.check_existing_schema()
        logging.info(f"📊 Current schema has {len(existing_schema['tables'])} tables")
        
        if args.verify_only:
            # Only verify existing deployment
            verification = await deployer.verify_deployment()
            if verification['validation_passed']:
                logging.info("✅ Deployment verification PASSED")
                return 0
            else:
                logging.error("❌ Deployment verification FAILED")
                return 1
        
        # Deploy schema
        success = await deployer.deploy_schema(force=args.force)
        
        if success:
            # Verify deployment
            verification = await deployer.verify_deployment()
            
            if verification['validation_passed']:
                # Set up maintenance
                await deployer.setup_maintenance_jobs()
                
                logging.info("🎉 Database schema deployment completed successfully!")
                
                # Print summary
                summary = deployer.get_deployment_summary()
                logging.info(f"📊 Deployment summary: {len(summary['deployment_log'])} operations")
                
                return 0
            else:
                logging.error("❌ Deployment verification failed")
                return 1
        else:
            logging.error("❌ Schema deployment failed")
            return 1
    
    except Exception as e:
        logging.error(f"❌ Deployment failed: {str(e)}")
        return 1
    
    finally:
        await deployer.disconnect()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
