"""
The Seer - Improved Strategy Analysis Service
============================================

Enhanced version with:
- Type-safe data models
- Eliminated code duplication
- Better error handling
- Comprehensive metrics
- Structured logging
"""

import os
import json
import logging
from typing import Dict, Any

# Add common path for shared utilities
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / 'common'))

from redis_subscriber import RedisSubscriberBase, SubscriptionConfig, MessageValidator
from models import UnlockEvent, TradeCandidate
from constants import TradingConstants, SystemConstants
from error_handling import ChimeraError, log_error_with_context

from analysis import calculate_unlock_pressure_score
from onchain_checker import is_token_borrowable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Configuration
REDIS_URL = os.environ.get("REDIS_URL")
UNLOCK_EVENT_CHANNEL = "chimera:unlock_events"
TRADE_CANDIDATE_CHANNEL = "chimera:trade_candidates"
PRESSURE_SCORE_THRESHOLD = float(os.environ.get("PRESSURE_SCORE_THRESHOLD", "0.75"))


class SeerService(RedisSubscriberBase):
    """
    The Seer - Strategy Analysis Service
    
    Analyzes unlock events and identifies high-conviction trade candidates
    based on pressure score calculations and borrowability checks.
    """
    
    def __init__(self):
        config = SubscriptionConfig(
            channel=UNLOCK_EVENT_CHANNEL,
            max_retries=SystemConstants.DEFAULT_MAX_RETRIES,
            retry_delay=SystemConstants.DEFAULT_RETRY_DELAY
        )
        super().__init__(config, REDIS_URL)
        
        # Service-specific metrics
        self.events_analyzed = 0
        self.candidates_published = 0
        self.events_rejected_score = 0
        self.events_rejected_borrowability = 0
        
        logging.info(f"🧠 The Seer initialized with threshold: {PRESSURE_SCORE_THRESHOLD}")
    
    def get_service_name(self) -> str:
        return "The Seer"
    
    def process_message(self, data: Dict[str, Any]) -> None:
        """Process unlock event and generate trade candidates"""
        try:
            # Validate message structure
            if not MessageValidator.validate_unlock_event(data):
                raise ChimeraError("Invalid unlock event structure")
            
            # Parse into structured model for validation
            unlock_event = UnlockEvent(**data)
            self.events_analyzed += 1
            
            logging.info(f"🧠 Analyzing unlock event: {unlock_event.token_symbol}")
            
            # 1. Calculate the Unlock Pressure Score
            score = calculate_unlock_pressure_score(data)  # Keep using dict for backward compatibility
            logging.info(f"📊 Pressure Score for {unlock_event.token_symbol}: {score:.2f}")

            if score < PRESSURE_SCORE_THRESHOLD:
                logging.info(f"❌ Score {score:.2f} below threshold {PRESSURE_SCORE_THRESHOLD}. Discarding.")
                self.events_rejected_score += 1
                return

            # 2. Check if the token is borrowable on a lending protocol
            if not is_token_borrowable(unlock_event.contract_address):
                logging.info(f"❌ Token {unlock_event.token_symbol} not borrowable. Discarding.")
                self.events_rejected_borrowability += 1
                return
            
            # 3. Create and publish trade candidate
            trade_candidate = TradeCandidate(
                token_symbol=unlock_event.token_symbol,
                contract_address=unlock_event.contract_address,
                unlock_date=unlock_event.unlock_date,
                strategy_id="pre_unlock_decay_v1",
                pressure_score=score,
                unlock_amount=unlock_event.unlock_amount,
                circulating_supply=unlock_event.circulating_supply,
                confidence=min(score / 2.0, 1.0),  # Convert score to confidence
                risk_level=self._determine_risk_level(score)
            )
            
            self._publish_trade_candidate(trade_candidate)
            self.candidates_published += 1
            
            logging.warning(f"🎯 HIGH CONVICTION: Published trade candidate for {unlock_event.token_symbol}")
            
        except Exception as e:
            log_error_with_context(
                e,
                context={
                    'service': 'The Seer',
                    'event_data': data,
                    'events_analyzed': self.events_analyzed
                }
            )
            raise
    
    def _determine_risk_level(self, pressure_score: float) -> str:
        """Determine risk level based on pressure score"""
        if pressure_score >= 2.0:
            return "HIGH"
        elif pressure_score >= 1.5:
            return "MEDIUM"
        else:
            return "LOW"
    
    def _publish_trade_candidate(self, candidate: TradeCandidate) -> None:
        """Publish trade candidate to Redis channel"""
        try:
            redis_client = self.get_redis_connection()
            message = candidate.json()
            redis_client.publish(TRADE_CANDIDATE_CHANNEL, message)
            
            logging.info(f"📤 Published trade candidate: {candidate.token_symbol}")
            
        except Exception as e:
            raise ChimeraError(f"Failed to publish trade candidate: {str(e)}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get enhanced service metrics"""
        base_metrics = super().get_metrics()
        
        # Add service-specific metrics
        base_metrics.update({
            'events_analyzed': self.events_analyzed,
            'candidates_published': self.candidates_published,
            'events_rejected_score': self.events_rejected_score,
            'events_rejected_borrowability': self.events_rejected_borrowability,
            'conversion_rate': (
                self.candidates_published / max(1, self.events_analyzed)
            ),
            'pressure_score_threshold': PRESSURE_SCORE_THRESHOLD
        })
        
        return base_metrics


def main():
    """Main entry point for The Seer service"""
    logging.info("🧠 Starting The Seer - Strategy Analysis Service")
    
    try:
        seer = SeerService()
        seer.listen()
        
    except KeyboardInterrupt:
        logging.info("🛑 Received shutdown signal")
    except Exception as e:
        logging.error(f"❌ Fatal error in The Seer: {e}")
        raise


if __name__ == "__main__":
    main()
