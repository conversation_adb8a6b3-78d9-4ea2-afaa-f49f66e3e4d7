"""
Redis Subscriber Base Class
==========================

Abstract base class to eliminate code duplication across Redis subscribers.
Provides common functionality for all services that listen to Redis channels.
"""

import json
import logging
import redis
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
from datetime import datetime, timezone

from .constants import SystemConstants, ErrorCodes
from .error_handling import (
    ChimeraError, 
    retry_with_backoff, 
    CircuitBreaker,
    log_error_with_context
)


@dataclass
class SubscriptionConfig:
    """Configuration for Redis subscription"""
    channel: str
    max_retries: int = SystemConstants.DEFAULT_MAX_RETRIES
    retry_delay: float = SystemConstants.DEFAULT_RETRY_DELAY
    circuit_breaker_threshold: int = SystemConstants.CIRCUIT_BREAKER_FAILURE_THRESHOLD
    health_check_interval: int = 60  # seconds
    message_timeout: int = 30  # seconds


class RedisSubscriberBase(ABC):
    """
    Abstract base class for Redis subscribers with dependency injection support.

    Provides common functionality:
    - Connection management with retry logic
    - Message processing with error handling
    - Health monitoring and circuit breaker
    - Graceful shutdown handling
    - Metrics collection
    - Dependency injection integration
    """

    def __init__(self, config: SubscriptionConfig, redis_url: Optional[str] = None):
        self.config = config
        self.redis_url = redis_url
        self.redis_client: Optional[redis.Redis] = None
        self.pubsub: Optional[redis.client.PubSub] = None
        self.running = False
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=config.circuit_breaker_threshold,
            recovery_timeout=30
        )

        # Metrics
        self.messages_processed = 0
        self.messages_failed = 0
        self.last_message_time: Optional[datetime] = None
        self.start_time = datetime.now(timezone.utc)

        # Health monitoring
        self.last_health_check = datetime.now(timezone.utc)

        # Service dependencies (to be injected by subclasses)
        self._dependencies: Dict[str, Any] = {}

        logging.info(f"Initialized Redis subscriber for channel: {config.channel}")

    def inject_dependency(self, name: str, service: Any):
        """Inject a service dependency"""
        self._dependencies[name] = service
        logging.debug(f"Injected dependency: {name}")

    def get_dependency(self, name: str) -> Any:
        """Get an injected dependency"""
        if name not in self._dependencies:
            raise ChimeraError(f"Dependency not found: {name}")
        return self._dependencies[name]
    
    @abstractmethod
    def process_message(self, data: Dict[str, Any]) -> None:
        """
        Process a message from the Redis channel.
        
        Args:
            data: Parsed JSON message data
            
        Raises:
            ChimeraError: If message processing fails
        """
        pass
    
    @abstractmethod
    def get_service_name(self) -> str:
        """Return the name of the service for logging"""
        pass
    
    def get_redis_connection(self) -> redis.Redis:
        """Get Redis connection with retry logic"""
        if self.redis_client is None:
            if not self.redis_url:
                raise ChimeraError(
                    "REDIS_URL environment variable not set",
                    error_code=ErrorCodes.CONFIGURATION_ERROR
                )
            
            self.redis_client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                socket_connect_timeout=10,
                socket_timeout=10,
                retry_on_timeout=True
            )
        
        return self.redis_client
    
    @retry_with_backoff(
        max_retries=SystemConstants.DEFAULT_MAX_RETRIES,
        base_delay=SystemConstants.DEFAULT_RETRY_DELAY,
        exceptions=(redis.RedisError, ConnectionError)
    )
    def _establish_subscription(self) -> None:
        """Establish Redis subscription with retry logic"""
        try:
            self.redis_client = self.get_redis_connection()
            self.pubsub = self.redis_client.pubsub(ignore_subscribe_messages=True)
            self.pubsub.subscribe(self.config.channel)
            
            logging.info(f"✅ {self.get_service_name()} subscribed to {self.config.channel}")
            
        except Exception as e:
            logging.error(f"❌ Failed to establish subscription: {e}")
            raise ChimeraError(
                f"Redis subscription failed: {str(e)}",
                error_code=ErrorCodes.DATA_SOURCE_UNAVAILABLE
            )
    
    def _parse_message(self, message: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse and validate incoming message"""
        try:
            if message['type'] != 'message':
                return None
            
            data = json.loads(message['data'])
            
            # Basic validation
            if not isinstance(data, dict):
                raise ValueError("Message data must be a dictionary")
            
            return data
            
        except json.JSONDecodeError as e:
            logging.error(f"❌ Invalid JSON in message: {message}. Error: {e}")
            self.messages_failed += 1
            return None
            
        except Exception as e:
            logging.error(f"❌ Failed to parse message: {message}. Error: {e}")
            self.messages_failed += 1
            return None
    
    def _handle_message(self, message: Dict[str, Any]) -> None:
        """Handle a single message with error handling"""
        try:
            data = self._parse_message(message)
            if data is None:
                return
            
            # Process message through circuit breaker
            self.circuit_breaker.call(self.process_message, data)
            
            # Update metrics
            self.messages_processed += 1
            self.last_message_time = datetime.now(timezone.utc)
            
            logging.debug(f"✅ Processed message: {data.get('type', 'unknown')}")
            
        except Exception as e:
            self.messages_failed += 1
            log_error_with_context(
                e, 
                context={
                    'service': self.get_service_name(),
                    'channel': self.config.channel,
                    'message': str(message)[:200]  # Truncate for logging
                }
            )
    
    def _perform_health_check(self) -> bool:
        """Perform health check on Redis connection"""
        try:
            if self.redis_client:
                self.redis_client.ping()
                return True
        except Exception as e:
            logging.warning(f"⚠️ Health check failed: {e}")
            return False
        
        return False
    
    def _should_perform_health_check(self) -> bool:
        """Check if it's time for a health check"""
        now = datetime.now(timezone.utc)
        elapsed = (now - self.last_health_check).total_seconds()
        return elapsed >= self.config.health_check_interval
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get subscriber metrics"""
        now = datetime.now(timezone.utc)
        uptime = (now - self.start_time).total_seconds()
        
        return {
            'service_name': self.get_service_name(),
            'channel': self.config.channel,
            'running': self.running,
            'uptime_seconds': uptime,
            'messages_processed': self.messages_processed,
            'messages_failed': self.messages_failed,
            'success_rate': (
                self.messages_processed / max(1, self.messages_processed + self.messages_failed)
            ),
            'last_message_time': self.last_message_time.isoformat() if self.last_message_time else None,
            'circuit_breaker_state': self.circuit_breaker.state
        }
    
    def listen(self) -> None:
        """
        Main listening loop with comprehensive error handling.
        
        Features:
        - Automatic reconnection on connection loss
        - Health monitoring with periodic checks
        - Graceful shutdown handling
        - Circuit breaker protection
        """
        service_name = self.get_service_name()
        logging.info(f"🎧 {service_name} starting to listen on {self.config.channel}...")
        
        self.running = True
        
        try:
            # Establish initial subscription
            self._establish_subscription()
            
            # Main message processing loop
            for message in self.pubsub.listen():
                if not self.running:
                    logging.info(f"🛑 {service_name} stopping...")
                    break
                
                # Handle the message
                self._handle_message(message)
                
                # Periodic health check
                if self._should_perform_health_check():
                    if not self._perform_health_check():
                        logging.warning(f"⚠️ Health check failed, attempting reconnection...")
                        self._establish_subscription()
                    
                    self.last_health_check = datetime.now(timezone.utc)
                    
                    # Log metrics periodically
                    metrics = self.get_metrics()
                    logging.info(f"📊 {service_name} metrics: "
                               f"processed={metrics['messages_processed']}, "
                               f"failed={metrics['messages_failed']}, "
                               f"success_rate={metrics['success_rate']:.2%}")
        
        except KeyboardInterrupt:
            logging.info(f"🛑 {service_name} received shutdown signal")
        
        except Exception as e:
            log_error_with_context(
                e,
                context={
                    'service': service_name,
                    'channel': self.config.channel,
                    'metrics': self.get_metrics()
                }
            )
            raise
        
        finally:
            self.shutdown()
    
    def shutdown(self) -> None:
        """Graceful shutdown of the subscriber"""
        service_name = self.get_service_name()
        logging.info(f"🔄 {service_name} shutting down...")
        
        self.running = False
        
        try:
            if self.pubsub:
                self.pubsub.unsubscribe(self.config.channel)
                self.pubsub.close()
                
            if self.redis_client:
                self.redis_client.close()
                
        except Exception as e:
            logging.error(f"❌ Error during shutdown: {e}")
        
        # Final metrics
        final_metrics = self.get_metrics()
        logging.info(f"📊 {service_name} final metrics: {final_metrics}")
        logging.info(f"✅ {service_name} shutdown complete")


class MessageValidator:
    """Utility class for message validation"""
    
    @staticmethod
    def validate_unlock_event(data: Dict[str, Any]) -> bool:
        """Validate unlock event message structure"""
        required_fields = ['token_symbol', 'contract_address', 'unlock_date']
        return all(field in data and data[field] for field in required_fields)
    
    @staticmethod
    def validate_trade_candidate(data: Dict[str, Any]) -> bool:
        """Validate trade candidate message structure"""
        required_fields = ['token_symbol', 'contract_address', 'pressure_score']
        return all(field in data and data[field] for field in required_fields)
    
    @staticmethod
    def validate_risk_alert(data: Dict[str, Any]) -> bool:
        """Validate risk alert message structure"""
        required_fields = ['position_id', 'alert_type', 'current_price']
        return all(field in data and data[field] for field in required_fields)


# Export classes for use by services
__all__ = [
    'RedisSubscriberBase',
    'SubscriptionConfig', 
    'MessageValidator'
]
