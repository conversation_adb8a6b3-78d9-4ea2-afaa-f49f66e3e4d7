import requests
import logging
from typing import Dict, Any
from decimal import Decimal

def calculate_unlock_pressure_score(event: Dict[str, Any]) -> Dict[str, Any]:
    """
    DEPRECATED: Naive pressure score replaced with advanced alpha model.

    This function now delegates to the sophisticated alpha model that includes:
    - Historical backtesting data
    - Holder behavior analysis
    - Derivatives market sentiment
    - Real execution cost validation
    """

    # Import the advanced alpha model
    try:
        from advanced_alpha_model import calculate_advanced_alpha_score

        # Use the advanced model
        alpha_result = calculate_advanced_alpha_score(event)

        # Convert to legacy format for backward compatibility
        legacy_score = alpha_result.get('alpha_score', 0.0)

        return {
            'pressure_score': legacy_score,
            'confidence_score': alpha_result.get('confidence_score', 0.0),
            'trade_recommendation': alpha_result.get('trade_recommendation', 'SKIP'),
            'expected_profit_pct': alpha_result.get('expected_profit_pct', 0.0),
            'risk_factors': alpha_result.get('risk_factors', {}),
            'advanced_analysis': alpha_result,
            'model_version': 'advanced_v2'
        }

    except ImportError:
        logging.error("⚠️ Advanced alpha model not available - using deprecated calculation")

        # Fallback to basic calculation with strong warnings
        try:
            token_symbol = event.get("token_symbol")
            contract_address = event.get("contract_address")
            unlock_amount = float(event.get("unlock_amount", 0.0))

            # Try to fetch real-time data from CoinGecko
            market_data = fetch_market_data(token_symbol, contract_address)

            if market_data:
                circulating_supply = market_data.get("circulating_supply", event.get("circulating_supply", 500000000.0))
                volume_24h = market_data.get("volume_24h", 100000000.0)
            else:
                # Fallback to event data or mock data
                circulating_supply = event.get("circulating_supply", 500000000.0)
                volume_24h = event.get("volume_24h", 100000000.0)

            if circulating_supply == 0 or volume_24h == 0:
                logging.warning(f"Invalid market data for {token_symbol}. Using fallback calculation.")
                return {
                    'pressure_score': 0.0,
                    'trade_recommendation': 'SKIP',
                    'error': 'invalid_market_data',
                    'model_version': 'deprecated_basic'
                }

            size_impact = unlock_amount / circulating_supply
            liquidity_impact = unlock_amount / volume_24h
            basic_score = size_impact * liquidity_impact

            logging.warning(f"🚨 Using DEPRECATED pressure score for {token_symbol}")
            logging.warning(f"🚨 This model has NEGATIVE expected value - DO NOT TRADE")

            return {
                'pressure_score': basic_score,
                'confidence_score': 0.2,  # Very low confidence
                'trade_recommendation': 'MANUAL_REVIEW_REQUIRED',  # Force manual review
                'expected_profit_pct': 0.0,  # Unknown with basic model
                'risk_factors': {
                    'model': 'deprecated_basic',
                    'warning': 'NEGATIVE_EXPECTED_VALUE'
                },
                'model_version': 'deprecated_basic',
                'warning': 'UPGRADE_TO_ADVANCED_MODEL_REQUIRED'
            }

        except Exception as e:
            logging.error(f"Error in fallback pressure score calculation: {e}")
            return {
                'pressure_score': 0.0,
                'confidence_score': 0.0,
                'trade_recommendation': 'SKIP',
                'error': str(e),
                'model_version': 'error'
            }

def fetch_market_data(token_symbol: str, contract_address: str) -> Dict[str, Any]:
    """
    Fetches real-time market data from CoinGecko API
    """
    try:
        # CoinGecko free API endpoint
        url = f"https://api.coingecko.com/api/v3/coins/ethereum/contract/{contract_address}"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            market_data = data.get("market_data", {})
            
            return {
                "circulating_supply": market_data.get("circulating_supply"),
                "total_supply": market_data.get("total_supply"),
                "volume_24h": market_data.get("total_volume", {}).get("usd"),
                "current_price": market_data.get("current_price", {}).get("usd"),
                "market_cap": market_data.get("market_cap", {}).get("usd")
            }
        else:
            logging.warning(f"CoinGecko API returned status {response.status_code} for {token_symbol}")
            return None
            
    except requests.RequestException as e:
        logging.warning(f"Error fetching market data for {token_symbol}: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error fetching market data: {e}")
        return None

def calculate_risk_metrics(event: Dict[str, Any]) -> Dict[str, float]:
    """
    Calculate additional risk metrics for the token
    """
    try:
        unlock_amount = float(event.get("unlock_amount", 0))
        circulating_supply = float(event.get("circulating_supply", 1))
        total_supply = float(event.get("total_supply", 1))
        
        # Calculate percentage of supply being unlocked
        unlock_percentage = (unlock_amount / circulating_supply) * 100 if circulating_supply > 0 else 0
        
        # Calculate how much of total supply is already circulating
        circulation_ratio = (circulating_supply / total_supply) * 100 if total_supply > 0 else 0
        
        # Calculate dilution impact
        new_circulating_supply = circulating_supply + unlock_amount
        dilution_impact = ((new_circulating_supply - circulating_supply) / circulating_supply) * 100 if circulating_supply > 0 else 0
        
        return {
            "unlock_percentage": unlock_percentage,
            "circulation_ratio": circulation_ratio,
            "dilution_impact": dilution_impact,
            "unlock_amount": unlock_amount,
            "circulating_supply": circulating_supply
        }
        
    except Exception as e:
        logging.error(f"Error calculating risk metrics: {e}")
        return {}

def is_high_impact_unlock(event: Dict[str, Any], threshold_percentage: float = 5.0) -> bool:
    """
    Determines if an unlock event represents a high-impact dilution event
    """
    try:
        risk_metrics = calculate_risk_metrics(event)
        unlock_percentage = risk_metrics.get("unlock_percentage", 0)
        
        return unlock_percentage >= threshold_percentage
        
    except Exception as e:
        logging.error(f"Error checking high impact unlock: {e}")
        return False
