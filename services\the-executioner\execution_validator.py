"""
Execution Validator - Pre-Trade Reality Checks
==============================================

This module performs critical pre-trade validation to ensure trades are
economically viable before execution. Prevents the system from attempting
trades that will lose money due to execution costs or market conditions.
"""

import logging
import requests
from decimal import Decimal
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
import json

class AaveValidator:
    """Validates borrowing conditions on Aave"""
    
    def __init__(self):
        self.aave_api_base = "https://aave-api-v2.aave.com"
        
    def check_borrow_availability(self, token_address: str, amount_needed: float) -> Dict[str, Any]:
        """Check if we can actually borrow the required amount"""
        
        try:
            # Query Aave V3 pool data
            response = requests.get(f"{self.aave_api_base}/data/reserves-incentives-v3")
            
            if response.status_code != 200:
                return {'available': False, 'reason': 'API_ERROR'}
            
            reserves_data = response.json()
            
            for reserve in reserves_data:
                if reserve['underlyingAsset'].lower() == token_address.lower():
                    
                    available_liquidity = float(reserve['availableLiquidity'])
                    utilization_rate = float(reserve['utilizationRate'])
                    variable_borrow_rate = float(reserve['variableBorrowRate'])
                    
                    # Critical checks
                    if available_liquidity < (amount_needed * 2):  # Need 2x buffer
                        return {
                            'available': False,
                            'reason': 'INSUFFICIENT_LIQUIDITY',
                            'available_liquidity': available_liquidity,
                            'needed': amount_needed
                        }
                    
                    if utilization_rate > 0.90:  # >90% utilization
                        return {
                            'available': False,
                            'reason': 'HIGH_UTILIZATION',
                            'utilization_rate': utilization_rate
                        }
                    
                    if variable_borrow_rate > 0.25:  # >25% APY
                        return {
                            'available': False,
                            'reason': 'HIGH_BORROW_RATE',
                            'borrow_rate': variable_borrow_rate
                        }
                    
                    return {
                        'available': True,
                        'available_liquidity': available_liquidity,
                        'utilization_rate': utilization_rate,
                        'borrow_rate': variable_borrow_rate,
                        'estimated_daily_cost': (variable_borrow_rate / 365) * amount_needed
                    }
            
            return {'available': False, 'reason': 'TOKEN_NOT_FOUND'}
            
        except Exception as e:
            logging.error(f"Aave validation failed: {e}")
            return {'available': False, 'reason': 'VALIDATION_ERROR', 'error': str(e)}


class SlippageValidator:
    """Validates slippage and execution costs"""
    
    def __init__(self):
        self.oneinch_api = "https://api.1inch.io/v5.0/1"  # Ethereum mainnet
        
    def check_swap_slippage(self, from_token: str, to_token: str, amount: float) -> Dict[str, Any]:
        """Check expected slippage for token swap"""
        
        try:
            # Convert amount to wei (assuming 18 decimals)
            amount_wei = int(amount * 10**18)
            
            # Query 1inch for quote
            quote_url = f"{self.oneinch_api}/quote"
            params = {
                'fromTokenAddress': from_token,
                'toTokenAddress': to_token,
                'amount': str(amount_wei)
            }
            
            response = requests.get(quote_url, params=params)
            
            if response.status_code != 200:
                return {'valid': False, 'reason': 'API_ERROR'}
            
            quote_data = response.json()
            
            estimated_gas = int(quote_data.get('estimatedGas', 300000))
            to_token_amount = float(quote_data['toTokenAmount']) / 10**18
            
            # Calculate slippage (simplified)
            expected_amount = amount  # 1:1 for stablecoin pairs, adjust for others
            slippage_pct = abs(expected_amount - to_token_amount) / expected_amount
            
            # Get current gas price
            gas_price_gwei = self._get_gas_price()
            gas_cost_eth = (estimated_gas * gas_price_gwei) / 10**9
            gas_cost_usd = gas_cost_eth * self._get_eth_price()
            
            if slippage_pct > 0.01:  # >1% slippage
                return {
                    'valid': False,
                    'reason': 'HIGH_SLIPPAGE',
                    'slippage_pct': slippage_pct,
                    'gas_cost_usd': gas_cost_usd
                }
            
            if gas_cost_usd > 200:  # >$200 gas cost
                return {
                    'valid': False,
                    'reason': 'HIGH_GAS_COST',
                    'gas_cost_usd': gas_cost_usd
                }
            
            return {
                'valid': True,
                'slippage_pct': slippage_pct,
                'gas_cost_usd': gas_cost_usd,
                'estimated_gas': estimated_gas,
                'to_token_amount': to_token_amount
            }
            
        except Exception as e:
            logging.error(f"Slippage validation failed: {e}")
            return {'valid': False, 'reason': 'VALIDATION_ERROR', 'error': str(e)}
    
    def _get_gas_price(self) -> float:
        """Get current gas price in Gwei"""
        try:
            response = requests.get("https://api.etherscan.io/api?module=gastracker&action=gasoracle")
            data = response.json()
            return float(data['result']['ProposeGasPrice'])
        except:
            return 30.0  # Default 30 Gwei
    
    def _get_eth_price(self) -> float:
        """Get current ETH price in USD"""
        try:
            response = requests.get("https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd")
            data = response.json()
            return float(data['ethereum']['usd'])
        except:
            return 3000.0  # Default $3000


class ExecutionValidator:
    """Main execution validator that combines all checks"""
    
    def __init__(self):
        self.aave_validator = AaveValidator()
        self.slippage_validator = SlippageValidator()
        
    def validate_trade_viability(self, trade_candidate: Dict[str, Any]) -> Dict[str, Any]:
        """Comprehensive pre-trade validation"""
        
        try:
            token_address = trade_candidate['contract_address']
            token_symbol = trade_candidate['token_symbol']
            position_size_usd = trade_candidate.get('position_size_usd', 10000)
            expected_profit_pct = trade_candidate.get('expected_profit_pct', 0.10)
            
            # Calculate token amount needed (assuming price from external API)
            token_price = self._get_token_price(token_symbol)
            if not token_price:
                return {'valid': False, 'reason': 'PRICE_UNAVAILABLE'}
            
            tokens_needed = position_size_usd / token_price
            
            validation_results = {
                'token_symbol': token_symbol,
                'position_size_usd': position_size_usd,
                'tokens_needed': tokens_needed,
                'token_price': token_price,
                'expected_profit_usd': position_size_usd * expected_profit_pct,
                'checks': {}
            }
            
            # 1. Aave Borrow Check
            borrow_check = self.aave_validator.check_borrow_availability(token_address, tokens_needed)
            validation_results['checks']['borrow'] = borrow_check
            
            if not borrow_check['available']:
                validation_results['valid'] = False
                validation_results['reason'] = f"BORROW_FAILED: {borrow_check['reason']}"
                return validation_results
            
            # 2. Slippage Check (Entry)
            usdc_address = "0xA0b86a33E6441b8C4505E2c8C5E6e8b8C4505E2c8"  # USDC
            entry_slippage = self.slippage_validator.check_swap_slippage(
                token_address, usdc_address, tokens_needed
            )
            validation_results['checks']['entry_slippage'] = entry_slippage
            
            if not entry_slippage['valid']:
                validation_results['valid'] = False
                validation_results['reason'] = f"ENTRY_SLIPPAGE: {entry_slippage['reason']}"
                return validation_results
            
            # 3. Slippage Check (Exit)
            exit_slippage = self.slippage_validator.check_swap_slippage(
                usdc_address, token_address, tokens_needed
            )
            validation_results['checks']['exit_slippage'] = exit_slippage
            
            if not exit_slippage['valid']:
                validation_results['valid'] = False
                validation_results['reason'] = f"EXIT_SLIPPAGE: {exit_slippage['reason']}"
                return validation_results
            
            # 4. Total Cost Analysis
            total_gas_cost = entry_slippage['gas_cost_usd'] + exit_slippage['gas_cost_usd']
            borrow_cost_daily = borrow_check['estimated_daily_cost'] * token_price
            estimated_trade_duration = 14  # days
            total_borrow_cost = borrow_cost_daily * estimated_trade_duration
            
            total_costs = total_gas_cost + total_borrow_cost
            expected_profit = validation_results['expected_profit_usd']
            net_profit = expected_profit - total_costs
            
            validation_results['cost_analysis'] = {
                'total_gas_cost_usd': total_gas_cost,
                'total_borrow_cost_usd': total_borrow_cost,
                'total_costs_usd': total_costs,
                'expected_profit_usd': expected_profit,
                'net_profit_usd': net_profit,
                'profit_margin_pct': (net_profit / position_size_usd) * 100
            }
            
            # 5. Profitability Check
            if net_profit <= 0:
                validation_results['valid'] = False
                validation_results['reason'] = 'UNPROFITABLE_AFTER_COSTS'
                return validation_results
            
            if (net_profit / position_size_usd) < 0.05:  # <5% net profit
                validation_results['valid'] = False
                validation_results['reason'] = 'INSUFFICIENT_PROFIT_MARGIN'
                return validation_results
            
            # All checks passed
            validation_results['valid'] = True
            validation_results['reason'] = 'ALL_CHECKS_PASSED'
            
            return validation_results
            
        except Exception as e:
            logging.error(f"Trade validation failed: {e}")
            return {
                'valid': False,
                'reason': 'VALIDATION_ERROR',
                'error': str(e)
            }
    
    def _get_token_price(self, token_symbol: str) -> Optional[float]:
        """Get current token price from CoinGecko"""
        try:
            # This would use a proper price feed in production
            response = requests.get(
                f"https://api.coingecko.com/api/v3/simple/price?ids={token_symbol.lower()}&vs_currencies=usd"
            )
            data = response.json()
            return float(list(data.values())[0]['usd'])
        except:
            return None


# Global validator instance
execution_validator = ExecutionValidator()


def validate_trade_execution(trade_candidate: Dict[str, Any]) -> Dict[str, Any]:
    """Main function to validate trade execution viability"""
    return execution_validator.validate_trade_viability(trade_candidate)
