"""
The Seer - Enhanced Strategy Analysis Service with Dependency Injection
======================================================================

Demonstrates the improved architecture with:
- Dependency injection for better testability
- Interface-based design
- Type-safe configuration
- Enhanced error handling
- Comprehensive metrics and monitoring
"""

import os
import logging
from typing import Dict, Any, Optional
import asyncio

# Add common path for shared utilities
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / 'common'))

from dependency_injection import (
    ServiceContainer, injectable, get_container,
    IPriceFetcher, INotificationService, IDataSource
)
from redis_subscriber import RedisSubscriberBase, SubscriptionConfig, MessageValidator
from models import UnlockEvent, TradeCandidate
from constants import TradingConstants, SystemConstants
from error_handling import ChimeraError, log_error_with_context
from secure_config import get_secure_config
from security import InputValidator, audit_logger

from analysis import calculate_unlock_pressure_score
from onchain_checker import is_token_borrowable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


@injectable(IDataSource)
class UnlockEventValidator:
    """Service for validating unlock events"""
    
    def __init__(self):
        self.events_validated = 0
        self.events_rejected = 0
    
    async def validate_data(self, data: Dict[str, Any]) -> bool:
        """Validate unlock event data"""
        try:
            self.events_validated += 1
            
            # Validate message structure
            if not MessageValidator.validate_unlock_event(data):
                self.events_rejected += 1
                return False
            
            # Validate token address format
            contract_address = data.get('contract_address', '')
            try:
                InputValidator.validate_ethereum_address(contract_address)
            except Exception:
                self.events_rejected += 1
                return False
            
            # Validate unlock amount
            unlock_amount = data.get('unlock_amount', 0)
            try:
                InputValidator.validate_decimal_amount(unlock_amount)
            except Exception:
                self.events_rejected += 1
                return False
            
            return True
            
        except Exception as e:
            logging.error(f"❌ Validation error: {e}")
            self.events_rejected += 1
            return False
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get validation metrics"""
        return {
            'events_validated': self.events_validated,
            'events_rejected': self.events_rejected,
            'rejection_rate': self.events_rejected / max(1, self.events_validated)
        }


@injectable(IPriceFetcher)
class MockPriceFetcher:
    """Mock price fetcher for testing"""
    
    async def get_price(self, token_address: str) -> Optional[float]:
        """Get mock price"""
        # Return mock price for testing
        return 100.0
    
    async def get_multiple_prices(self, token_addresses: list) -> Dict[str, float]:
        """Get mock prices for multiple tokens"""
        return {addr: 100.0 for addr in token_addresses}


@injectable(INotificationService)
class EnhancedNotificationService:
    """Enhanced notification service with multiple channels"""
    
    def __init__(self):
        self.notifications_sent = 0
        self.alerts_sent = 0
        self.config = get_secure_config()
    
    async def send_notification(self, message: str, priority: int = 2) -> bool:
        """Send notification"""
        try:
            self.notifications_sent += 1
            
            # Log notification (in real implementation, would send to Telegram, etc.)
            logging.info(f"📢 NOTIFICATION [P{priority}]: {message}")
            
            # Audit log for high priority notifications
            if priority >= 3:
                audit_logger.log_security_event(
                    "HIGH_PRIORITY_NOTIFICATION",
                    {"message": message[:100], "priority": priority}
                )
            
            return True
            
        except Exception as e:
            logging.error(f"❌ Notification failed: {e}")
            return False
    
    async def send_alert(self, alert_type: str, details: Dict[str, Any]) -> bool:
        """Send alert notification"""
        try:
            self.alerts_sent += 1
            
            message = f"🚨 ALERT [{alert_type}]: {details}"
            logging.warning(message)
            
            # Audit log all alerts
            audit_logger.log_security_event(
                "ALERT_SENT",
                {"alert_type": alert_type, "details": details},
                severity="WARNING"
            )
            
            return True
            
        except Exception as e:
            logging.error(f"❌ Alert failed: {e}")
            return False
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get notification metrics"""
        return {
            'notifications_sent': self.notifications_sent,
            'alerts_sent': self.alerts_sent
        }


class EnhancedSeerService(RedisSubscriberBase):
    """
    Enhanced Seer service with dependency injection and improved architecture
    """
    
    def __init__(self, container: ServiceContainer):
        # Load configuration
        config = get_secure_config()
        
        # Initialize base class
        subscription_config = SubscriptionConfig(
            channel="chimera:unlock_events",
            max_retries=SystemConstants.DEFAULT_MAX_RETRIES,
            retry_delay=SystemConstants.DEFAULT_RETRY_DELAY
        )
        super().__init__(subscription_config, config.redis.url)
        
        # Inject dependencies
        self.validator = container.resolve(UnlockEventValidator)
        self.price_fetcher = container.resolve(MockPriceFetcher)
        self.notification_service = container.resolve(EnhancedNotificationService)
        
        # Service-specific configuration
        self.pressure_score_threshold = config.trading.pressure_score_threshold
        
        # Enhanced metrics
        self.events_analyzed = 0
        self.candidates_published = 0
        self.events_rejected_score = 0
        self.events_rejected_borrowability = 0
        self.events_rejected_validation = 0
        
        logging.info(f"🧠 Enhanced Seer initialized with threshold: {self.pressure_score_threshold}")
    
    def get_service_name(self) -> str:
        return "Enhanced Seer"
    
    async def process_message(self, data: Dict[str, Any]) -> None:
        """Process unlock event with enhanced validation and error handling"""
        try:
            # Step 1: Validate input data
            if not await self.validator.validate_data(data):
                self.events_rejected_validation += 1
                await self.notification_service.send_notification(
                    f"❌ Invalid unlock event data received", priority=1
                )
                return
            
            # Step 2: Parse into structured model
            unlock_event = UnlockEvent(**data)
            self.events_analyzed += 1
            
            logging.info(f"🧠 Analyzing unlock event: {unlock_event.token_symbol}")
            
            # Step 3: Calculate pressure score
            score = calculate_unlock_pressure_score(data)
            logging.info(f"📊 Pressure Score for {unlock_event.token_symbol}: {score:.2f}")
            
            if score < self.pressure_score_threshold:
                logging.info(f"❌ Score {score:.2f} below threshold {self.pressure_score_threshold}")
                self.events_rejected_score += 1
                return
            
            # Step 4: Check borrowability
            if not is_token_borrowable(unlock_event.contract_address):
                logging.info(f"❌ Token {unlock_event.token_symbol} not borrowable")
                self.events_rejected_borrowability += 1
                return
            
            # Step 5: Get current price for additional validation
            current_price = await self.price_fetcher.get_price(unlock_event.contract_address)
            if not current_price or current_price <= 0:
                logging.warning(f"⚠️ Could not get valid price for {unlock_event.token_symbol}")
            
            # Step 6: Create trade candidate
            trade_candidate = TradeCandidate(
                token_symbol=unlock_event.token_symbol,
                contract_address=unlock_event.contract_address,
                unlock_date=unlock_event.unlock_date,
                strategy_id="pre_unlock_decay_v1",
                pressure_score=score,
                unlock_amount=unlock_event.unlock_amount,
                circulating_supply=unlock_event.circulating_supply,
                confidence=min(score / 2.0, 1.0),
                risk_level=self._determine_risk_level(score)
            )
            
            # Step 7: Publish trade candidate
            await self._publish_trade_candidate(trade_candidate)
            self.candidates_published += 1
            
            # Step 8: Send high-priority notification
            await self.notification_service.send_alert(
                "TRADE_CANDIDATE_GENERATED",
                {
                    "token": unlock_event.token_symbol,
                    "pressure_score": score,
                    "confidence": trade_candidate.confidence
                }
            )
            
            logging.warning(f"🎯 HIGH CONVICTION: Published trade candidate for {unlock_event.token_symbol}")
            
            # Audit log for trade candidates
            audit_logger.log_security_event(
                "TRADE_CANDIDATE_CREATED",
                {
                    "token_symbol": unlock_event.token_symbol,
                    "pressure_score": score,
                    "risk_level": trade_candidate.risk_level
                }
            )
            
        except Exception as e:
            log_error_with_context(
                e,
                context={
                    'service': 'Enhanced Seer',
                    'event_data': data,
                    'events_analyzed': self.events_analyzed
                }
            )
            
            # Send error notification
            await self.notification_service.send_alert(
                "PROCESSING_ERROR",
                {"error": str(e), "event": data.get('token_symbol', 'unknown')}
            )
            
            raise
    
    def _determine_risk_level(self, pressure_score: float) -> str:
        """Determine risk level based on pressure score"""
        if pressure_score >= 2.0:
            return "HIGH"
        elif pressure_score >= 1.5:
            return "MEDIUM"
        else:
            return "LOW"
    
    async def _publish_trade_candidate(self, candidate: TradeCandidate) -> None:
        """Publish trade candidate to Redis channel"""
        try:
            redis_client = self.get_redis_connection()
            message = candidate.json()
            redis_client.publish("chimera:trade_candidates", message)
            
            logging.info(f"📤 Published trade candidate: {candidate.token_symbol}")
            
        except Exception as e:
            raise ChimeraError(f"Failed to publish trade candidate: {str(e)}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get comprehensive service metrics"""
        base_metrics = super().get_metrics()
        
        # Add service-specific metrics
        base_metrics.update({
            'events_analyzed': self.events_analyzed,
            'candidates_published': self.candidates_published,
            'events_rejected_score': self.events_rejected_score,
            'events_rejected_borrowability': self.events_rejected_borrowability,
            'events_rejected_validation': self.events_rejected_validation,
            'conversion_rate': (
                self.candidates_published / max(1, self.events_analyzed)
            ),
            'pressure_score_threshold': self.pressure_score_threshold,
            'validator_metrics': self.validator.get_metrics(),
            'notification_metrics': self.notification_service.get_metrics()
        })
        
        return base_metrics


async def main():
    """Main entry point with dependency injection setup"""
    logging.info("🧠 Starting Enhanced Seer - Strategy Analysis Service")
    
    try:
        # Setup dependency injection container
        container = get_container()
        
        # Register services
        container.register_singleton(UnlockEventValidator)
        container.register_singleton(MockPriceFetcher)
        container.register_singleton(EnhancedNotificationService)
        
        # Create and start the service
        seer = EnhancedSeerService(container)
        
        # Start listening (this would be async in a real implementation)
        seer.listen()
        
    except KeyboardInterrupt:
        logging.info("🛑 Received shutdown signal")
    except Exception as e:
        logging.error(f"❌ Fatal error in Enhanced Seer: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
