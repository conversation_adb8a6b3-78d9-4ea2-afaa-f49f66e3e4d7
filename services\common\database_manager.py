"""
Unified Database Manager - Eliminates Database Code Duplication
==============================================================

This module consolidates all database operations across the Project Chimera
microservices, eliminating the duplicate database connection and query code
found in multiple services.

Features:
- Unified connection management for PostgreSQL and SQLite
- Standardized query execution patterns
- Connection pooling and retry logic
- Consistent error handling
- Type-safe query operations
"""

import os
import logging
import sqlite3
import asyncio
import asyncpg
import psycopg2
import psycopg2.extras
from contextlib import asynccontextmanager
from typing import Dict, Any, List, Optional, Union, Tuple
from datetime import datetime, timedelta
from urllib.parse import urlparse
from decimal import Decimal

from .constants import DatabaseConstants, SystemConstants
from .error_handling import ChimeraError, retry_with_backoff, log_error_with_context
from .models import Position, UnlockEvent, PositionStatus


class DatabaseConnectionManager:
    """Unified database connection manager for all services"""
    
    def __init__(self, database_url: Optional[str] = None):
        self.database_url = database_url or os.environ.get("DATABASE_URL")
        if not self.database_url:
            raise ChimeraError("DATABASE_URL environment variable not set")
        
        # Parse database URL to determine type
        parsed = urlparse(self.database_url)
        self.db_type = parsed.scheme
        
        if self.db_type == 'sqlite':
            self.db_path = self.database_url.replace('sqlite:///', '')
        elif self.db_type == 'postgresql':
            self.pg_config = {
                'host': parsed.hostname,
                'port': parsed.port,
                'database': parsed.path.lstrip('/'),
                'user': parsed.username,
                'password': parsed.password
            }
        else:
            raise ChimeraError(f"Unsupported database type: {self.db_type}")
        
        logging.info(f"✅ Database manager initialized for {self.db_type}")
    
    def get_sync_connection(self):
        """Get synchronous database connection"""
        if self.db_type == 'sqlite':
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            return conn
        elif self.db_type == 'postgresql':
            return psycopg2.connect(self.database_url)
        else:
            raise ChimeraError(f"Unsupported database type: {self.db_type}")
    
    @retry_with_backoff(max_retries=3, base_delay=1.0, exceptions=(psycopg2.Error, sqlite3.Error))
    def execute_query(self, query: str, params: Optional[Tuple] = None, fetch: str = 'all') -> Union[List[Dict], Dict, None]:
        """Execute query with unified error handling and retry logic"""
        conn = self.get_sync_connection()
        try:
            if self.db_type == 'sqlite':
                cursor = conn.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if fetch == 'all':
                    results = cursor.fetchall()
                    return [dict(row) for row in results]
                elif fetch == 'one':
                    result = cursor.fetchone()
                    return dict(result) if result else None
                else:
                    conn.commit()
                    return cursor.rowcount
                    
            elif self.db_type == 'postgresql':
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)
                    
                    if fetch == 'all':
                        results = cursor.fetchall()
                        return [dict(row) for row in results]
                    elif fetch == 'one':
                        result = cursor.fetchone()
                        return dict(result) if result else None
                    else:
                        conn.commit()
                        return cursor.rowcount
                        
        except Exception as e:
            log_error_with_context(e, context={'query': query[:100], 'db_type': self.db_type})
            raise ChimeraError(f"Database query failed: {str(e)}")
        finally:
            conn.close()


class AsyncDatabaseManager:
    """Async database manager with connection pooling"""
    
    def __init__(self, database_url: Optional[str] = None):
        self.database_url = database_url or os.environ.get("DATABASE_URL")
        if not self.database_url:
            raise ChimeraError("DATABASE_URL environment variable not set")
        
        self.pool: Optional[asyncpg.Pool] = None
        self._prepared_statements = {}
    
    async def initialize(self):
        """Initialize async connection pool"""
        try:
            self.pool = await asyncpg.create_pool(
                self.database_url,
                min_size=DatabaseConstants.MIN_POOL_SIZE,
                max_size=DatabaseConstants.MAX_POOL_SIZE,
                command_timeout=DatabaseConstants.DEFAULT_QUERY_TIMEOUT,
                server_settings={
                    'application_name': 'chimera_trading_system',
                    'tcp_keepalives_idle': '600',
                    'tcp_keepalives_interval': '30',
                    'tcp_keepalives_count': '3',
                }
            )
            
            await self._prepare_common_statements()
            logging.info(f"✅ Async database pool initialized")
            
        except Exception as e:
            logging.error(f"❌ Failed to initialize async database pool: {e}")
            raise ChimeraError(f"Async database initialization failed: {str(e)}")
    
    async def _prepare_common_statements(self):
        """Prepare commonly used SQL statements"""
        statements = {
            'get_open_positions': """
                SELECT id as position_id, token_symbol, token_address,
                       amount_shorted, entry_price_in_usdc, unlock_date, status,
                       borrow_tx_hash, swap_tx_hash, created_at
                FROM positions
                WHERE status = 'OPEN'
                ORDER BY created_at ASC
            """,
            
            'insert_position': """
                INSERT INTO positions (token_symbol, token_address, amount_shorted,
                                     entry_price_in_usdc, unlock_date, status,
                                     borrow_tx_hash, swap_tx_hash, pressure_score)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                RETURNING id
            """,
            
            'update_position_status': """
                UPDATE positions 
                SET status = $2, closed_at = $3, close_tx_hash = $4, updated_at = NOW()
                WHERE id = $1
            """,
            
            'get_upcoming_unlocks': """
                SELECT * FROM unlock_events
                WHERE unlock_date BETWEEN NOW() AND NOW() + INTERVAL '%s days'
                ORDER BY unlock_date ASC
            """
        }
        
        if self.pool:
            async with self.pool.acquire() as conn:
                for name, query in statements.items():
                    try:
                        await conn.prepare(query)
                        self._prepared_statements[name] = query
                        logging.debug(f"✅ Prepared statement: {name}")
                    except Exception as e:
                        logging.warning(f"⚠️ Failed to prepare statement {name}: {e}")
    
    @asynccontextmanager
    async def acquire_connection(self):
        """Context manager for acquiring database connections"""
        if not self.pool:
            raise ChimeraError("Database pool not initialized")
        
        async with self.pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                log_error_with_context(e, context={'database_operation': True})
                raise
    
    @retry_with_backoff(max_retries=3, base_delay=1.0, exceptions=(asyncpg.PostgresError,))
    async def execute_query(self, query: str, *args, fetch: str = 'none') -> Union[List[Dict], Dict, None]:
        """Execute async query with retry logic"""
        async with self.acquire_connection() as conn:
            try:
                if fetch == 'all':
                    result = await conn.fetch(query, *args)
                    return [dict(row) for row in result]
                elif fetch == 'one':
                    result = await conn.fetchrow(query, *args)
                    return dict(result) if result else None
                elif fetch == 'val':
                    return await conn.fetchval(query, *args)
                else:
                    await conn.execute(query, *args)
                    return None
                    
            except asyncpg.PostgresError as e:
                log_error_with_context(
                    e, 
                    context={
                        'query': query[:100],
                        'args_count': len(args)
                    }
                )
                raise ChimeraError(f"Async database query failed: {str(e)}")
    
    async def close(self):
        """Close the connection pool"""
        if self.pool:
            await self.pool.close()
            logging.info("✅ Async database pool closed")


# Global instances
_sync_db_manager: Optional[DatabaseConnectionManager] = None
_async_db_manager: Optional[AsyncDatabaseManager] = None


def get_database_manager() -> DatabaseConnectionManager:
    """Get the global synchronous database manager instance"""
    global _sync_db_manager
    if _sync_db_manager is None:
        _sync_db_manager = DatabaseConnectionManager()
    return _sync_db_manager


async def get_async_database_manager() -> AsyncDatabaseManager:
    """Get the global asynchronous database manager instance"""
    global _async_db_manager
    if _async_db_manager is None:
        _async_db_manager = AsyncDatabaseManager()
        await _async_db_manager.initialize()
    return _async_db_manager


async def cleanup_database_connections():
    """Cleanup all database connections"""
    global _async_db_manager
    if _async_db_manager:
        await _async_db_manager.close()
    logging.info("✅ Database cleanup complete")


# Unified Database Operations - Replaces duplicate functions across services
class UnifiedDatabaseOperations:
    """Unified database operations that replace duplicate code across all services"""

    def __init__(self, db_manager: DatabaseConnectionManager):
        self.db = db_manager

    def get_open_positions(self) -> List[Dict[str, Any]]:
        """Get all open trading positions - replaces duplicate implementations"""
        if self.db.db_type == 'sqlite':
            query = """
                SELECT id as position_id, token_symbol, contract_address,
                       amount_borrowed, entry_price_in_usdc, status,
                       borrow_tx_hash, swap_tx_hash, opened_at as created_at
                FROM positions
                WHERE status = 'OPEN'
                ORDER BY opened_at ASC
            """
        else:  # PostgreSQL
            query = """
                SELECT id as position_id, token_symbol, token_address,
                       amount_shorted, entry_price_in_usdc, unlock_date, status,
                       borrow_tx_hash, swap_tx_hash, created_at
                FROM positions
                WHERE status = 'OPEN'
                ORDER BY created_at ASC
            """

        return self.db.execute_query(query, fetch='all')

    def insert_position(self, position_data: Dict[str, Any]) -> int:
        """Insert new position - unified implementation"""
        if self.db.db_type == 'sqlite':
            query = """
                INSERT INTO positions (token_symbol, contract_address, amount_borrowed,
                                     entry_price_in_usdc, status, borrow_tx_hash,
                                     swap_tx_hash, opened_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                position_data['token_symbol'],
                position_data.get('contract_address', position_data.get('token_address')),
                position_data.get('amount_borrowed', position_data.get('amount_shorted')),
                position_data['entry_price_in_usdc'],
                position_data.get('status', 'OPEN'),
                position_data.get('borrow_tx_hash'),
                position_data.get('swap_tx_hash'),
                datetime.now()
            )
        else:  # PostgreSQL
            query = """
                INSERT INTO positions (token_symbol, token_address, amount_shorted,
                                     entry_price_in_usdc, unlock_date, status,
                                     borrow_tx_hash, swap_tx_hash, pressure_score)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """
            params = (
                position_data['token_symbol'],
                position_data['token_address'],
                position_data['amount_shorted'],
                position_data['entry_price_in_usdc'],
                position_data.get('unlock_date'),
                position_data.get('status', 'OPEN'),
                position_data.get('borrow_tx_hash'),
                position_data.get('swap_tx_hash'),
                position_data.get('pressure_score')
            )

        result = self.db.execute_query(query, params, fetch='none')
        return result if isinstance(result, int) else 1

    def update_position_status(self, position_id: int, status: str, close_tx_hash: Optional[str] = None) -> bool:
        """Update position status - unified implementation"""
        if self.db.db_type == 'sqlite':
            query = """
                UPDATE positions
                SET status = ?, closed_at = ?, close_tx_hash = ?
                WHERE id = ?
            """
            params = (status, datetime.now() if status == 'CLOSED' else None, close_tx_hash, position_id)
        else:  # PostgreSQL
            query = """
                UPDATE positions
                SET status = %s, closed_at = %s, close_tx_hash = %s, updated_at = NOW()
                WHERE id = %s
            """
            params = (status, datetime.now() if status == 'CLOSED' else None, close_tx_hash, position_id)

        rows_affected = self.db.execute_query(query, params, fetch='none')
        return rows_affected > 0

    def get_position_by_id(self, position_id: int) -> Optional[Dict[str, Any]]:
        """Get position by ID - unified implementation"""
        if self.db.db_type == 'sqlite':
            query = "SELECT * FROM positions WHERE id = ?"
        else:  # PostgreSQL
            query = "SELECT * FROM positions WHERE id = %s"

        return self.db.execute_query(query, (position_id,), fetch='one')

    def store_unlock_events(self, events: List[Dict[str, Any]]) -> int:
        """Store unlock events - unified implementation"""
        if not events:
            return 0

        stored_count = 0
        for event in events:
            try:
                if self.db.db_type == 'sqlite':
                    query = """
                        INSERT OR IGNORE INTO unlock_events
                        (token_symbol, contract_address, unlock_date, unlock_amount,
                         circulating_supply, total_supply, source)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """
                    params = (
                        event['token_symbol'],
                        event['contract_address'],
                        event['unlock_date'],
                        event['unlock_amount'],
                        event.get('circulating_supply'),
                        event.get('total_supply'),
                        event.get('source', 'unknown')
                    )
                else:  # PostgreSQL
                    query = """
                        INSERT INTO unlock_events
                        (token_symbol, contract_address, unlock_date, unlock_amount,
                         circulating_supply, total_supply, source)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (contract_address, unlock_date) DO NOTHING
                    """
                    params = (
                        event['token_symbol'],
                        event['contract_address'],
                        event['unlock_date'],
                        event['unlock_amount'],
                        event.get('circulating_supply'),
                        event.get('total_supply'),
                        event.get('source', 'unknown')
                    )

                rows_affected = self.db.execute_query(query, params, fetch='none')
                if rows_affected > 0:
                    stored_count += 1

            except Exception as e:
                logging.error(f"Error storing unlock event {event.get('token_symbol', 'unknown')}: {e}")

        logging.info(f"✅ Stored {stored_count}/{len(events)} unlock events")
        return stored_count

    def get_upcoming_unlocks(self, days_ahead: int = 14) -> List[Dict[str, Any]]:
        """Get upcoming unlock events - unified implementation"""
        if self.db.db_type == 'sqlite':
            query = """
                SELECT * FROM unlock_events
                WHERE unlock_date BETWEEN datetime('now') AND datetime('now', '+{} days')
                ORDER BY unlock_date ASC
            """.format(days_ahead)
        else:  # PostgreSQL
            query = """
                SELECT * FROM unlock_events
                WHERE unlock_date BETWEEN NOW() AND NOW() + INTERVAL '%s days'
                ORDER BY unlock_date ASC
            """

        params = (days_ahead,) if self.db.db_type == 'postgresql' else None
        return self.db.execute_query(query, params, fetch='all')

    def insert_trade_history(self, trade_data: Dict[str, Any]) -> bool:
        """Insert trade history record - unified implementation"""
        try:
            if self.db.db_type == 'sqlite':
                query = """
                    INSERT INTO trade_history
                    (position_id, action, price, amount, timestamp, tx_hash)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                params = (
                    trade_data['position_id'],
                    trade_data['action'],
                    trade_data['price'],
                    trade_data['amount'],
                    datetime.now(),
                    trade_data.get('tx_hash')
                )
            else:  # PostgreSQL
                query = """
                    INSERT INTO trade_history
                    (position_id, action, price, amount, timestamp, tx_hash)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """
                params = (
                    trade_data['position_id'],
                    trade_data['action'],
                    trade_data['price'],
                    trade_data['amount'],
                    datetime.now(),
                    trade_data.get('tx_hash')
                )

            rows_affected = self.db.execute_query(query, params, fetch='none')
            return rows_affected > 0

        except Exception as e:
            logging.error(f"Error inserting trade history: {e}")
            return False

    def insert_risk_event(self, risk_data: Dict[str, Any]) -> bool:
        """Insert risk management event - unified implementation"""
        try:
            if self.db.db_type == 'sqlite':
                query = """
                    INSERT INTO risk_alerts
                    (position_id, alert_type, alert_message, current_price, trigger_price)
                    VALUES (?, ?, ?, ?, ?)
                """
                params = (
                    risk_data['position_id'],
                    risk_data['alert_type'],
                    risk_data['alert_message'],
                    risk_data.get('current_price'),
                    risk_data.get('trigger_price')
                )
            else:  # PostgreSQL
                query = """
                    INSERT INTO risk_alerts
                    (position_id, alert_type, alert_message, current_price, trigger_price)
                    VALUES (%s, %s, %s, %s, %s)
                """
                params = (
                    risk_data['position_id'],
                    risk_data['alert_type'],
                    risk_data['alert_message'],
                    risk_data.get('current_price'),
                    risk_data.get('trigger_price')
                )

            rows_affected = self.db.execute_query(query, params, fetch='none')
            return rows_affected > 0

        except Exception as e:
            logging.error(f"Error inserting risk event: {e}")
            return False


# Global unified operations instance
_unified_db_ops: Optional[UnifiedDatabaseOperations] = None


def get_unified_db_operations() -> UnifiedDatabaseOperations:
    """Get the global unified database operations instance"""
    global _unified_db_ops
    if _unified_db_ops is None:
        db_manager = get_database_manager()
        _unified_db_ops = UnifiedDatabaseOperations(db_manager)
    return _unified_db_ops
