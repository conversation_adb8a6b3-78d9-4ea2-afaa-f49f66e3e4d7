"""
Unified Utilities - Eliminates Duplicate Utility Functions
==========================================================

This module consolidates common utility functions found across multiple
services, eliminating code duplication and providing a centralized
location for shared functionality.

Features:
- Date/time utilities
- Data validation functions
- JSON serialization helpers
- Price formatting utilities
- Common calculation functions
"""

import json
import logging
from datetime import datetime, timezone, timedelta
from decimal import Decimal, ROUND_HALF_UP
from typing import Any, Dict, List, Optional, Union
import re


class DateTimeUtils:
    """Unified date/time utilities that replace duplicate implementations"""
    
    @staticmethod
    def now_utc() -> datetime:
        """Get current UTC datetime - replaces duplicate datetime.now() calls"""
        return datetime.now(timezone.utc)
    
    @staticmethod
    def to_iso_string(dt: datetime) -> str:
        """Convert datetime to ISO string - replaces duplicate isoformat() calls"""
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        return dt.isoformat()
    
    @staticmethod
    def from_iso_string(iso_string: str) -> datetime:
        """Parse ISO string to datetime - replaces duplicate parsing logic"""
        try:
            return datetime.fromisoformat(iso_string.replace('Z', '+00:00'))
        except ValueError:
            # Fallback for different ISO formats
            return datetime.strptime(iso_string, '%Y-%m-%dT%H:%M:%S.%fZ').replace(tzinfo=timezone.utc)
    
    @staticmethod
    def days_until(target_date: datetime) -> float:
        """Calculate days until target date - replaces duplicate calculations"""
        if isinstance(target_date, str):
            target_date = DateTimeUtils.from_iso_string(target_date)
        
        now = DateTimeUtils.now_utc()
        if target_date.tzinfo is None:
            target_date = target_date.replace(tzinfo=timezone.utc)
        
        delta = target_date - now
        return delta.total_seconds() / 86400  # seconds in a day
    
    @staticmethod
    def add_days(dt: datetime, days: int) -> datetime:
        """Add days to datetime - replaces duplicate timedelta operations"""
        return dt + timedelta(days=days)
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """Format duration in human-readable format"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds/60:.1f}m"
        elif seconds < 86400:
            return f"{seconds/3600:.1f}h"
        else:
            return f"{seconds/86400:.1f}d"


class DataValidationUtils:
    """Unified data validation utilities that replace duplicate validation code"""
    
    @staticmethod
    def is_valid_ethereum_address(address: str) -> bool:
        """Validate Ethereum address format - replaces duplicate validation"""
        if not address or not isinstance(address, str):
            return False
        
        # Check if it's a valid hex string with 0x prefix and 40 characters
        pattern = r'^0x[a-fA-F0-9]{40}$'
        return bool(re.match(pattern, address))
    
    @staticmethod
    def is_valid_transaction_hash(tx_hash: str) -> bool:
        """Validate transaction hash format - replaces duplicate validation"""
        if not tx_hash or not isinstance(tx_hash, str):
            return False
        
        # Check if it's a valid hex string with 0x prefix and 64 characters
        pattern = r'^0x[a-fA-F0-9]{64}$'
        return bool(re.match(pattern, tx_hash))
    
    @staticmethod
    def is_valid_token_symbol(symbol: str) -> bool:
        """Validate token symbol format - replaces duplicate validation"""
        if not symbol or not isinstance(symbol, str):
            return False
        
        # Token symbols are typically 2-10 uppercase letters
        pattern = r'^[A-Z]{2,10}$'
        return bool(re.match(pattern, symbol.upper()))
    
    @staticmethod
    def validate_unlock_event(event: Dict[str, Any]) -> bool:
        """Validate unlock event data structure - replaces duplicate validation"""
        required_fields = ['token_symbol', 'contract_address', 'unlock_date', 'unlock_amount']
        
        for field in required_fields:
            if field not in event or event[field] is None:
                logging.warning(f"Missing required field: {field}")
                return False
        
        # Validate specific fields
        if not DataValidationUtils.is_valid_token_symbol(event['token_symbol']):
            logging.warning(f"Invalid token symbol: {event['token_symbol']}")
            return False
        
        if not DataValidationUtils.is_valid_ethereum_address(event['contract_address']):
            logging.warning(f"Invalid contract address: {event['contract_address']}")
            return False
        
        try:
            unlock_amount = float(event['unlock_amount'])
            if unlock_amount <= 0:
                logging.warning(f"Invalid unlock amount: {unlock_amount}")
                return False
        except (ValueError, TypeError):
            logging.warning(f"Invalid unlock amount format: {event['unlock_amount']}")
            return False
        
        return True
    
    @staticmethod
    def validate_position_data(position: Dict[str, Any]) -> bool:
        """Validate position data structure - replaces duplicate validation"""
        required_fields = ['token_symbol', 'amount_shorted', 'entry_price_in_usdc']
        
        for field in required_fields:
            if field not in position or position[field] is None:
                logging.warning(f"Missing required field: {field}")
                return False
        
        try:
            amount = float(position['amount_shorted'])
            price = float(position['entry_price_in_usdc'])
            
            if amount <= 0 or price <= 0:
                logging.warning(f"Invalid position amounts: {amount}, {price}")
                return False
        except (ValueError, TypeError):
            logging.warning("Invalid position amount format")
            return False
        
        return True


class JSONUtils:
    """Unified JSON utilities that replace duplicate serialization code"""
    
    @staticmethod
    def safe_serialize(obj: Any) -> str:
        """Safely serialize object to JSON - replaces duplicate serialization logic"""
        def json_serializer(obj):
            """Custom JSON serializer for complex objects"""
            if isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, Decimal):
                return float(obj)
            elif hasattr(obj, '__dict__'):
                return obj.__dict__
            else:
                return str(obj)
        
        try:
            return json.dumps(obj, default=json_serializer, ensure_ascii=False)
        except Exception as e:
            logging.error(f"JSON serialization failed: {e}")
            return json.dumps({"error": "serialization_failed", "message": str(e)})
    
    @staticmethod
    def safe_deserialize(json_str: str) -> Optional[Dict[str, Any]]:
        """Safely deserialize JSON string - replaces duplicate parsing logic"""
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logging.error(f"JSON deserialization failed: {e}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error during JSON parsing: {e}")
            return None


class PriceUtils:
    """Unified price utilities that replace duplicate price formatting code"""
    
    @staticmethod
    def format_price(price: Union[float, Decimal, str], decimals: int = 4) -> str:
        """Format price with consistent decimal places - replaces duplicate formatting"""
        try:
            if isinstance(price, str):
                price = Decimal(price)
            elif isinstance(price, float):
                price = Decimal(str(price))
            
            # Round to specified decimal places
            quantizer = Decimal('0.1') ** decimals
            rounded_price = price.quantize(quantizer, rounding=ROUND_HALF_UP)
            
            return f"${rounded_price:,.{decimals}f}"
        except (ValueError, TypeError, Exception):
            return f"${price}"
    
    @staticmethod
    def format_percentage(value: Union[float, Decimal], decimals: int = 2) -> str:
        """Format percentage with consistent decimal places - replaces duplicate formatting"""
        try:
            if isinstance(value, Decimal):
                value = float(value)
            
            return f"{value * 100:.{decimals}f}%"
        except (ValueError, TypeError):
            return f"{value}%"
    
    @staticmethod
    def calculate_percentage_change(old_price: float, new_price: float) -> float:
        """Calculate percentage change - replaces duplicate calculations"""
        try:
            if old_price == 0:
                return 0.0
            return (new_price - old_price) / old_price
        except (ValueError, TypeError, ZeroDivisionError):
            return 0.0
    
    @staticmethod
    def format_large_number(number: Union[int, float], decimals: int = 2) -> str:
        """Format large numbers with K/M/B suffixes - replaces duplicate formatting"""
        try:
            num = float(number)
            
            if abs(num) >= 1_000_000_000:
                return f"{num / 1_000_000_000:.{decimals}f}B"
            elif abs(num) >= 1_000_000:
                return f"{num / 1_000_000:.{decimals}f}M"
            elif abs(num) >= 1_000:
                return f"{num / 1_000:.{decimals}f}K"
            else:
                return f"{num:.{decimals}f}"
        except (ValueError, TypeError):
            return str(number)


class CalculationUtils:
    """Unified calculation utilities that replace duplicate calculation code"""
    
    @staticmethod
    def calculate_pressure_score_base(unlock_amount: float, circulating_supply: float, 
                                    days_until_unlock: float) -> float:
        """Base pressure score calculation - replaces duplicate implementations"""
        try:
            if circulating_supply <= 0 or days_until_unlock <= 0:
                return 0.0
            
            # Basic pressure score formula
            supply_impact = unlock_amount / circulating_supply
            time_factor = max(0.1, 1.0 / days_until_unlock)  # Higher pressure closer to unlock
            
            base_score = supply_impact * time_factor
            
            # Normalize to 0-1 range
            return min(1.0, max(0.0, base_score))
        except (ValueError, TypeError, ZeroDivisionError):
            return 0.0
    
    @staticmethod
    def calculate_position_pnl(entry_price: float, current_price: float, 
                             amount: float, is_short: bool = True) -> Dict[str, float]:
        """Calculate position P&L - replaces duplicate calculations"""
        try:
            if is_short:
                # For short positions, profit when price goes down
                price_change = entry_price - current_price
            else:
                # For long positions, profit when price goes up
                price_change = current_price - entry_price
            
            pnl_absolute = price_change * amount
            pnl_percentage = price_change / entry_price if entry_price > 0 else 0.0
            
            return {
                'pnl_absolute': pnl_absolute,
                'pnl_percentage': pnl_percentage,
                'pnl_percentage_formatted': PriceUtils.format_percentage(pnl_percentage),
                'current_value': current_price * amount,
                'entry_value': entry_price * amount
            }
        except (ValueError, TypeError, ZeroDivisionError):
            return {
                'pnl_absolute': 0.0,
                'pnl_percentage': 0.0,
                'pnl_percentage_formatted': '0.00%',
                'current_value': 0.0,
                'entry_value': 0.0
            }
    
    @staticmethod
    def calculate_risk_levels(entry_price: float, stop_loss_pct: float, 
                            take_profit_pct: float, is_short: bool = True) -> Dict[str, float]:
        """Calculate risk management levels - replaces duplicate calculations"""
        try:
            if is_short:
                # For short positions
                stop_loss_price = entry_price * (1 + stop_loss_pct)
                take_profit_price = entry_price * (1 - take_profit_pct)
            else:
                # For long positions
                stop_loss_price = entry_price * (1 - stop_loss_pct)
                take_profit_price = entry_price * (1 + take_profit_pct)
            
            return {
                'entry_price': entry_price,
                'stop_loss_price': stop_loss_price,
                'take_profit_price': take_profit_price,
                'stop_loss_pct': stop_loss_pct,
                'take_profit_pct': take_profit_pct
            }
        except (ValueError, TypeError):
            return {
                'entry_price': entry_price,
                'stop_loss_price': entry_price,
                'take_profit_price': entry_price,
                'stop_loss_pct': 0.0,
                'take_profit_pct': 0.0
            }


class LoggingUtils:
    """Unified logging utilities that replace duplicate logging patterns"""
    
    @staticmethod
    def log_service_start(service_name: str, config_summary: Dict[str, Any] = None):
        """Log service startup - replaces duplicate startup logging"""
        logging.info(f"🚀 Starting {service_name}")
        if config_summary:
            logging.info(f"📋 Configuration: {config_summary}")
    
    @staticmethod
    def log_service_stop(service_name: str, metrics: Dict[str, Any] = None):
        """Log service shutdown - replaces duplicate shutdown logging"""
        logging.info(f"🛑 Stopping {service_name}")
        if metrics:
            logging.info(f"📊 Final metrics: {metrics}")
    
    @staticmethod
    def log_operation_success(operation: str, details: Dict[str, Any] = None):
        """Log successful operation - replaces duplicate success logging"""
        message = f"✅ {operation} completed successfully"
        if details:
            message += f": {details}"
        logging.info(message)
    
    @staticmethod
    def log_operation_failure(operation: str, error: Exception, context: Dict[str, Any] = None):
        """Log failed operation - replaces duplicate error logging"""
        message = f"❌ {operation} failed: {str(error)}"
        if context:
            message += f" | Context: {context}"
        logging.error(message)
    
    @staticmethod
    def log_metrics(service_name: str, metrics: Dict[str, Any]):
        """Log service metrics - replaces duplicate metrics logging"""
        formatted_metrics = []
        for key, value in metrics.items():
            if isinstance(value, float):
                formatted_metrics.append(f"{key}={value:.2f}")
            else:
                formatted_metrics.append(f"{key}={value}")
        
        logging.info(f"📊 {service_name} metrics: {', '.join(formatted_metrics)}")


# Export all utility classes for easy importing
__all__ = [
    'DateTimeUtils',
    'DataValidationUtils', 
    'JSONUtils',
    'PriceUtils',
    'CalculationUtils',
    'LoggingUtils'
]
