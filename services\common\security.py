"""
Security Module - Enhanced Input Validation and Secret Management
================================================================

Comprehensive security features:
- Input validation and sanitization
- Secret management with encryption
- API security and rate limiting
- Audit logging and monitoring
- Security headers and CSRF protection
"""

import os
import re
import hashlib
import hmac
import secrets
import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone, timedelta
from decimal import Decimal, InvalidOperation
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import json

from .constants import ValidationConstants, ErrorCodes
from .error_handling import ChimeraError, log_error_with_context


class SecurityError(ChimeraError):
    """Security-related error"""
    def __init__(self, message: str, **kwargs):
        super().__init__(message, error_code=ErrorCodes.VALIDATION_ERROR, **kwargs)


class InputValidator:
    """
    Comprehensive input validation and sanitization
    """
    
    @staticmethod
    def validate_ethereum_address(address: str) -> str:
        """Validate and normalize Ethereum address"""
        if not address:
            raise SecurityError("Address cannot be empty")
        
        # Remove whitespace and convert to lowercase
        address = address.strip().lower()
        
        # Check format
        if not re.match(ValidationConstants.ETHEREUM_ADDRESS_PATTERN, address):
            raise SecurityError(f"Invalid Ethereum address format: {address}")
        
        # Additional checksum validation could be added here
        return address
    
    @staticmethod
    def validate_transaction_hash(tx_hash: str) -> str:
        """Validate transaction hash format"""
        if not tx_hash:
            raise SecurityError("Transaction hash cannot be empty")
        
        tx_hash = tx_hash.strip().lower()
        
        if not re.match(ValidationConstants.TRANSACTION_HASH_PATTERN, tx_hash):
            raise SecurityError(f"Invalid transaction hash format: {tx_hash}")
        
        return tx_hash
    
    @staticmethod
    def validate_token_symbol(symbol: str) -> str:
        """Validate token symbol"""
        if not symbol:
            raise SecurityError("Token symbol cannot be empty")
        
        symbol = symbol.strip().upper()
        
        if len(symbol) < ValidationConstants.MIN_SYMBOL_LENGTH:
            raise SecurityError(f"Token symbol too short: {symbol}")
        
        if len(symbol) > ValidationConstants.MAX_SYMBOL_LENGTH:
            raise SecurityError(f"Token symbol too long: {symbol}")
        
        if not re.match(ValidationConstants.SYMBOL_PATTERN, symbol):
            raise SecurityError(f"Invalid token symbol format: {symbol}")
        
        return symbol
    
    @staticmethod
    def validate_decimal_amount(amount: Union[str, float, Decimal], 
                              min_value: Optional[Decimal] = None,
                              max_value: Optional[Decimal] = None) -> Decimal:
        """Validate and convert decimal amounts"""
        try:
            if isinstance(amount, str):
                # Remove any whitespace and validate format
                amount = amount.strip()
                if not re.match(r'^\d+(\.\d+)?$', amount):
                    raise SecurityError(f"Invalid decimal format: {amount}")
            
            decimal_amount = Decimal(str(amount))
            
            # Check for NaN or infinity
            if not decimal_amount.is_finite():
                raise SecurityError("Amount must be a finite number")
            
            # Apply range validation
            min_val = min_value or ValidationConstants.MIN_AMOUNT
            max_val = max_value or ValidationConstants.MAX_AMOUNT
            
            if decimal_amount < min_val:
                raise SecurityError(f"Amount {decimal_amount} below minimum {min_val}")
            
            if decimal_amount > max_val:
                raise SecurityError(f"Amount {decimal_amount} above maximum {max_val}")
            
            return decimal_amount
            
        except (InvalidOperation, ValueError) as e:
            raise SecurityError(f"Invalid decimal amount: {amount}")
    
    @staticmethod
    def validate_price(price: Union[str, float, Decimal]) -> Decimal:
        """Validate price values"""
        return InputValidator.validate_decimal_amount(
            price,
            min_value=ValidationConstants.MIN_PRICE,
            max_value=ValidationConstants.MAX_PRICE
        )
    
    @staticmethod
    def validate_percentage(percentage: Union[str, float]) -> float:
        """Validate percentage values (0-100)"""
        try:
            pct = float(percentage)
            
            if not 0 <= pct <= 100:
                raise SecurityError(f"Percentage must be between 0 and 100: {pct}")
            
            return pct
            
        except (ValueError, TypeError):
            raise SecurityError(f"Invalid percentage value: {percentage}")
    
    @staticmethod
    def sanitize_string(text: str, max_length: int = 1000, 
                       allow_html: bool = False) -> str:
        """Sanitize string input"""
        if not isinstance(text, str):
            raise SecurityError("Input must be a string")
        
        # Remove null bytes and control characters
        text = ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')
        
        # Limit length
        if len(text) > max_length:
            text = text[:max_length]
        
        # Remove HTML if not allowed
        if not allow_html:
            text = re.sub(r'<[^>]+>', '', text)
        
        return text.strip()
    
    @staticmethod
    def validate_json_structure(data: Dict[str, Any], 
                               required_fields: List[str],
                               optional_fields: List[str] = None) -> Dict[str, Any]:
        """Validate JSON structure and required fields"""
        if not isinstance(data, dict):
            raise SecurityError("Data must be a dictionary")
        
        # Check required fields
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise SecurityError(f"Missing required fields: {missing_fields}")
        
        # Check for unexpected fields
        allowed_fields = set(required_fields + (optional_fields or []))
        unexpected_fields = set(data.keys()) - allowed_fields
        if unexpected_fields:
            raise SecurityError(f"Unexpected fields: {list(unexpected_fields)}")
        
        return data


class SecretManager:
    """
    Secure secret management with encryption
    """
    
    def __init__(self, master_key: Optional[str] = None):
        self.master_key = master_key or os.environ.get('MASTER_KEY')
        self._cipher_suite = None
        self._initialize_encryption()
    
    def _initialize_encryption(self):
        """Initialize encryption cipher"""
        if not self.master_key:
            # Generate a new key if none provided
            self.master_key = Fernet.generate_key().decode()
            logging.warning("⚠️ Generated new master key - store securely!")
        
        try:
            # Derive key from master key
            if isinstance(self.master_key, str):
                master_key_bytes = self.master_key.encode()
            else:
                master_key_bytes = self.master_key
            
            # Use PBKDF2 for key derivation
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'chimera_salt',  # In production, use random salt
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(master_key_bytes))
            self._cipher_suite = Fernet(key)
            
        except Exception as e:
            raise SecurityError(f"Failed to initialize encryption: {str(e)}")
    
    def encrypt_secret(self, secret: str) -> str:
        """Encrypt a secret value"""
        try:
            if not self._cipher_suite:
                raise SecurityError("Encryption not initialized")
            
            encrypted = self._cipher_suite.encrypt(secret.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
            
        except Exception as e:
            raise SecurityError(f"Failed to encrypt secret: {str(e)}")
    
    def decrypt_secret(self, encrypted_secret: str) -> str:
        """Decrypt a secret value"""
        try:
            if not self._cipher_suite:
                raise SecurityError("Encryption not initialized")
            
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_secret.encode())
            decrypted = self._cipher_suite.decrypt(encrypted_bytes)
            return decrypted.decode()
            
        except Exception as e:
            raise SecurityError(f"Failed to decrypt secret: {str(e)}")
    
    def get_secret(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """Get and decrypt a secret from environment"""
        encrypted_value = os.environ.get(key, default)
        
        if not encrypted_value:
            return None
        
        # Check if value is encrypted (starts with our prefix)
        if encrypted_value.startswith('ENC:'):
            return self.decrypt_secret(encrypted_value[4:])
        else:
            # Plain text value (for development)
            logging.warning(f"⚠️ Secret {key} is not encrypted")
            return encrypted_value
    
    def set_secret(self, key: str, value: str, encrypt: bool = True) -> str:
        """Set a secret value (optionally encrypted)"""
        if encrypt:
            encrypted_value = f"ENC:{self.encrypt_secret(value)}"
            os.environ[key] = encrypted_value
            return encrypted_value
        else:
            os.environ[key] = value
            return value


class APISecurityManager:
    """
    API security features including rate limiting and authentication
    """
    
    def __init__(self):
        self.api_keys: Dict[str, Dict[str, Any]] = {}
        self.request_logs: List[Dict[str, Any]] = []
        self.blocked_ips: set = set()
    
    def generate_api_key(self, user_id: str, permissions: List[str]) -> str:
        """Generate a new API key"""
        api_key = secrets.token_urlsafe(32)
        
        self.api_keys[api_key] = {
            'user_id': user_id,
            'permissions': permissions,
            'created_at': datetime.now(timezone.utc),
            'last_used': None,
            'request_count': 0,
            'active': True
        }
        
        return api_key
    
    def validate_api_key(self, api_key: str, required_permission: str = None) -> bool:
        """Validate API key and permissions"""
        if api_key not in self.api_keys:
            return False
        
        key_info = self.api_keys[api_key]
        
        if not key_info['active']:
            return False
        
        if required_permission and required_permission not in key_info['permissions']:
            return False
        
        # Update usage statistics
        key_info['last_used'] = datetime.now(timezone.utc)
        key_info['request_count'] += 1
        
        return True
    
    def create_request_signature(self, method: str, url: str, body: str, 
                                secret_key: str) -> str:
        """Create HMAC signature for request authentication"""
        message = f"{method.upper()}{url}{body}"
        signature = hmac.new(
            secret_key.encode(),
            message.encode(),
            hashlib.sha256
        ).hexdigest()
        
        return signature
    
    def verify_request_signature(self, signature: str, method: str, 
                                url: str, body: str, secret_key: str) -> bool:
        """Verify request signature"""
        expected_signature = self.create_request_signature(method, url, body, secret_key)
        return hmac.compare_digest(signature, expected_signature)
    
    def log_request(self, ip_address: str, endpoint: str, 
                   status_code: int, user_agent: str = None):
        """Log API request for monitoring"""
        log_entry = {
            'timestamp': datetime.now(timezone.utc),
            'ip_address': ip_address,
            'endpoint': endpoint,
            'status_code': status_code,
            'user_agent': user_agent
        }
        
        self.request_logs.append(log_entry)
        
        # Keep only recent logs (last 1000 requests)
        if len(self.request_logs) > 1000:
            self.request_logs = self.request_logs[-1000:]
    
    def detect_suspicious_activity(self, ip_address: str) -> bool:
        """Detect suspicious activity patterns"""
        recent_requests = [
            log for log in self.request_logs
            if log['ip_address'] == ip_address
            and log['timestamp'] > datetime.now(timezone.utc) - timedelta(minutes=10)
        ]
        
        # Check for too many requests
        if len(recent_requests) > 100:
            return True
        
        # Check for too many errors
        error_requests = [log for log in recent_requests if log['status_code'] >= 400]
        if len(error_requests) > 20:
            return True
        
        return False
    
    def block_ip(self, ip_address: str, reason: str):
        """Block an IP address"""
        self.blocked_ips.add(ip_address)
        logging.warning(f"🚫 Blocked IP {ip_address}: {reason}")
    
    def is_ip_blocked(self, ip_address: str) -> bool:
        """Check if IP is blocked"""
        return ip_address in self.blocked_ips


class AuditLogger:
    """
    Security audit logging
    """
    
    def __init__(self):
        self.audit_logs: List[Dict[str, Any]] = []
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], 
                          severity: str = "INFO"):
        """Log security-related events"""
        audit_entry = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'event_type': event_type,
            'severity': severity,
            'details': details,
            'source': 'chimera_security'
        }
        
        self.audit_logs.append(audit_entry)
        
        # Log to standard logging as well
        log_message = f"SECURITY [{severity}] {event_type}: {json.dumps(details)}"
        
        if severity == "CRITICAL":
            logging.critical(log_message)
        elif severity == "ERROR":
            logging.error(log_message)
        elif severity == "WARNING":
            logging.warning(log_message)
        else:
            logging.info(log_message)
    
    def log_authentication_attempt(self, user_id: str, success: bool, 
                                  ip_address: str = None):
        """Log authentication attempts"""
        self.log_security_event(
            "AUTHENTICATION_ATTEMPT",
            {
                'user_id': user_id,
                'success': success,
                'ip_address': ip_address
            },
            severity="WARNING" if not success else "INFO"
        )
    
    def log_permission_check(self, user_id: str, permission: str, 
                           granted: bool):
        """Log permission checks"""
        self.log_security_event(
            "PERMISSION_CHECK",
            {
                'user_id': user_id,
                'permission': permission,
                'granted': granted
            },
            severity="WARNING" if not granted else "DEBUG"
        )
    
    def log_data_access(self, user_id: str, resource: str, action: str):
        """Log data access events"""
        self.log_security_event(
            "DATA_ACCESS",
            {
                'user_id': user_id,
                'resource': resource,
                'action': action
            }
        )


# Global instances
secret_manager = SecretManager()
api_security = APISecurityManager()
audit_logger = AuditLogger()


# Export public interface
__all__ = [
    'SecurityError',
    'InputValidator',
    'SecretManager',
    'APISecurityManager',
    'AuditLogger',
    'secret_manager',
    'api_security',
    'audit_logger'
]
