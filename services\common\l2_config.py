"""
L2 Configuration - Arbitrum/Polygon Migration
=============================================

This module configures the system to operate on Layer 2 networks where
gas costs are economically viable for the trading strategy.
"""

import os
from typing import Dict, Any
from enum import Enum

class Network(Enum):
    ETHEREUM = "ethereum"
    ARBITRUM = "arbitrum"
    POLYGON = "polygon"
    OPTIMISM = "optimism"

class L2Config:
    """Layer 2 network configuration for viable economics"""
    
    NETWORK_CONFIGS = {
        Network.ARBITRUM: {
            'chain_id': 42161,
            'rpc_url': 'https://arb1.arbitrum.io/rpc',
            'aave_pool': '******************************************',
            'usdc_address': '******************************************',
            'weth_address': '******************************************',
            'oneinch_router': '******************************************',
            'gas_cost_multiplier': 0.01,  # 1% of mainnet costs
            'min_position_size': 1000,    # $1,000 minimum
            'max_position_size': 50000,   # $50,000 maximum
            'supported_tokens': [
                'ARB', 'GMX', 'MAGIC', 'DPX', 'RDNT', 'GRAIL'
            ]
        },
        
        Network.POLYGON: {
            'chain_id': 137,
            'rpc_url': 'https://polygon-rpc.com',
            'aave_pool': '******************************************',
            'usdc_address': '******************************************',
            'weth_address': '******************************************',
            'oneinch_router': '******************************************',
            'gas_cost_multiplier': 0.001,  # 0.1% of mainnet costs
            'min_position_size': 500,     # $500 minimum
            'max_position_size': 25000,   # $25,000 maximum
            'supported_tokens': [
                'MATIC', 'QUICK', 'DQUICK', 'GHST', 'SAND'
            ]
        },
        
        Network.ETHEREUM: {
            'chain_id': 1,
            'rpc_url': os.environ.get('INFURA_URL', ''),
            'aave_pool': '******************************************',
            'usdc_address': '0xA0b86a33E6441b8C4505E2c8C5E6e8b8C4505E2c8',
            'weth_address': '******************************************',
            'oneinch_router': '******************************************',
            'gas_cost_multiplier': 1.0,   # Full mainnet costs
            'min_position_size': 10000,   # $10,000 minimum (due to gas costs)
            'max_position_size': 100000,  # $100,000 maximum
            'supported_tokens': [
                'UNI', 'AAVE', 'COMP', 'MKR', 'SNX', 'CRV', 'LDO'
            ]
        }
    }
    
    def __init__(self):
        # Default to Arbitrum for better economics
        self.current_network = Network.ARBITRUM
        self.config = self.NETWORK_CONFIGS[self.current_network]
        
        # Override from environment
        network_env = os.environ.get('TRADING_NETWORK', 'arbitrum').lower()
        if network_env in [n.value for n in Network]:
            self.current_network = Network(network_env)
            self.config = self.NETWORK_CONFIGS[self.current_network]
    
    def get_network_config(self) -> Dict[str, Any]:
        """Get current network configuration"""
        return {
            'network': self.current_network.value,
            'chain_id': self.config['chain_id'],
            'rpc_url': self.config['rpc_url'],
            'contracts': {
                'aave_pool': self.config['aave_pool'],
                'usdc': self.config['usdc_address'],
                'weth': self.config['weth_address'],
                'oneinch_router': self.config['oneinch_router']
            },
            'economics': {
                'gas_cost_multiplier': self.config['gas_cost_multiplier'],
                'min_position_size': self.config['min_position_size'],
                'max_position_size': self.config['max_position_size']
            },
            'supported_tokens': self.config['supported_tokens']
        }
    
    def is_token_supported(self, token_symbol: str) -> bool:
        """Check if token is supported on current network"""
        return token_symbol.upper() in self.config['supported_tokens']
    
    def calculate_gas_costs(self, base_mainnet_cost: float) -> float:
        """Calculate gas costs for current network"""
        return base_mainnet_cost * self.config['gas_cost_multiplier']
    
    def validate_position_size(self, position_size_usd: float) -> bool:
        """Validate position size for current network"""
        return (self.config['min_position_size'] <= 
                position_size_usd <= 
                self.config['max_position_size'])


class NetworkOptimizer:
    """Optimizes network selection based on trade characteristics"""
    
    def __init__(self):
        self.l2_config = L2Config()
    
    def select_optimal_network(self, trade_candidate: Dict[str, Any]) -> Network:
        """Select optimal network for a trade"""
        
        token_symbol = trade_candidate.get('token_symbol', '').upper()
        position_size = trade_candidate.get('position_size_usd', 10000)
        expected_profit_pct = trade_candidate.get('expected_profit_pct', 0.10)
        
        # Check which networks support this token
        available_networks = []
        for network, config in L2Config.NETWORK_CONFIGS.items():
            if token_symbol in config['supported_tokens']:
                available_networks.append(network)
        
        if not available_networks:
            # Token not supported on any L2, must use mainnet
            return Network.ETHEREUM
        
        # Calculate economics for each available network
        best_network = None
        best_net_profit = -1
        
        for network in available_networks:
            config = L2Config.NETWORK_CONFIGS[network]
            
            # Estimate gas costs
            base_gas_cost = 400  # $400 on mainnet for full cycle
            network_gas_cost = base_gas_cost * config['gas_cost_multiplier']
            
            # Check position size limits
            if not (config['min_position_size'] <= position_size <= config['max_position_size']):
                continue
            
            # Calculate net profit
            gross_profit = position_size * expected_profit_pct
            net_profit = gross_profit - network_gas_cost
            
            if net_profit > best_net_profit:
                best_net_profit = net_profit
                best_network = network
        
        return best_network or Network.ETHEREUM
    
    def get_network_recommendation(self, trade_candidate: Dict[str, Any]) -> Dict[str, Any]:
        """Get detailed network recommendation"""
        
        optimal_network = self.select_optimal_network(trade_candidate)
        config = L2Config.NETWORK_CONFIGS[optimal_network]
        
        position_size = trade_candidate.get('position_size_usd', 10000)
        expected_profit_pct = trade_candidate.get('expected_profit_pct', 0.10)
        
        # Calculate economics
        base_gas_cost = 400
        network_gas_cost = base_gas_cost * config['gas_cost_multiplier']
        gross_profit = position_size * expected_profit_pct
        net_profit = gross_profit - network_gas_cost
        
        return {
            'recommended_network': optimal_network.value,
            'chain_id': config['chain_id'],
            'economics': {
                'position_size_usd': position_size,
                'expected_gross_profit': gross_profit,
                'estimated_gas_cost': network_gas_cost,
                'estimated_net_profit': net_profit,
                'profit_margin_pct': (net_profit / position_size) * 100,
                'gas_cost_pct': (network_gas_cost / position_size) * 100
            },
            'viable': net_profit > 0 and (net_profit / position_size) > 0.03,  # >3% net margin
            'contracts': {
                'aave_pool': config['aave_pool'],
                'usdc': config['usdc_address'],
                'oneinch_router': config['oneinch_router']
            }
        }


# Global instances
l2_config = L2Config()
network_optimizer = NetworkOptimizer()


def get_current_network_config() -> Dict[str, Any]:
    """Get current network configuration"""
    return l2_config.get_network_config()


def optimize_network_for_trade(trade_candidate: Dict[str, Any]) -> Dict[str, Any]:
    """Get optimal network recommendation for a trade"""
    return network_optimizer.get_network_recommendation(trade_candidate)
