"""
Project Chimera - Data Models
============================

Type-safe data models using Pydantic for validation and serialization.
Replaces loose dictionaries with structured, validated data classes.
"""

from datetime import datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field, validator, root_validator
import re

from .constants import ValidationConstants, TradingConstants, ErrorCodes


# =============================================================================
# ENUMS
# =============================================================================

class PositionStatus(str, Enum):
    """Position status enumeration"""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PENDING = "PENDING"
    FAILED = "FAILED"


class AlertType(str, Enum):
    """Risk alert type enumeration"""
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"
    TIME_EXIT = "TIME_EXIT"
    HIGH_RISK = "HIGH_RISK"
    CRITICAL_RISK = "CRITICAL_RISK"


class MarketCondition(str, Enum):
    """Market condition enumeration"""
    FAVORABLE = "FAVORABLE"
    NEUTRAL = "NEUTRAL"
    UNFAVORABLE = "UNFAVORABLE"


class SlippageRisk(str, Enum):
    """Slippage risk level enumeration"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"


class ExecutionSignalType(str, Enum):
    """Execution signal type enumeration"""
    EXECUTE_NOW = "EXECUTE_NOW"
    WAIT_FOR_DIP = "WAIT_FOR_DIP"
    CANCEL = "CANCEL"


# =============================================================================
# BASE MODELS
# =============================================================================

class ChimeraBaseModel(BaseModel):
    """Base model with common configuration"""
    
    class Config:
        # Use enum values instead of names
        use_enum_values = True
        # Allow arbitrary types (for Decimal)
        arbitrary_types_allowed = True
        # Validate on assignment
        validate_assignment = True
        # JSON encoders for custom types
        json_encoders = {
            Decimal: lambda v: float(v),
            datetime: lambda v: v.isoformat()
        }


# =============================================================================
# CORE TRADING MODELS
# =============================================================================

class TokenInfo(ChimeraBaseModel):
    """Token information model"""
    symbol: str = Field(..., min_length=ValidationConstants.MIN_SYMBOL_LENGTH, 
                        max_length=ValidationConstants.MAX_SYMBOL_LENGTH)
    contract_address: str = Field(..., regex=ValidationConstants.ETHEREUM_ADDRESS_PATTERN)
    decimals: int = Field(default=18, ge=0, le=30)
    name: Optional[str] = None
    
    @validator('symbol')
    def validate_symbol(cls, v):
        if not re.match(ValidationConstants.SYMBOL_PATTERN, v):
            raise ValueError('Token symbol must contain only uppercase letters and numbers')
        return v.upper()
    
    @validator('contract_address')
    def validate_address(cls, v):
        return v.lower()  # Normalize to lowercase


class UnlockEvent(ChimeraBaseModel):
    """Token unlock event model"""
    token_symbol: str
    contract_address: str = Field(..., regex=ValidationConstants.ETHEREUM_ADDRESS_PATTERN)
    unlock_date: datetime
    unlock_amount: Decimal = Field(..., gt=0)
    circulating_supply: Optional[Decimal] = Field(None, gt=0)
    total_supply: Optional[Decimal] = Field(None, gt=0)
    pressure_score: Optional[float] = Field(None, ge=0, le=10)
    volume_24h: Optional[Decimal] = Field(None, ge=0)
    market_cap: Optional[Decimal] = Field(None, ge=0)
    source: Optional[str] = None
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    @validator('contract_address')
    def validate_address(cls, v):
        return v.lower()
    
    @root_validator
    def validate_supplies(cls, values):
        circulating = values.get('circulating_supply')
        total = values.get('total_supply')
        
        if circulating and total and circulating > total:
            raise ValueError('Circulating supply cannot exceed total supply')
        
        return values


class TradeCandidate(ChimeraBaseModel):
    """Trade candidate model"""
    token_symbol: str
    contract_address: str = Field(..., regex=ValidationConstants.ETHEREUM_ADDRESS_PATTERN)
    unlock_date: datetime
    strategy_id: str = "pre_unlock_decay_v1"
    pressure_score: float = Field(..., ge=0, le=10)
    unlock_amount: Decimal = Field(..., gt=0)
    circulating_supply: Optional[Decimal] = Field(None, gt=0)
    confidence: Optional[float] = Field(None, ge=0, le=1)
    risk_level: Optional[str] = "MEDIUM"
    
    @validator('contract_address')
    def validate_address(cls, v):
        return v.lower()


class Position(ChimeraBaseModel):
    """Trading position model"""
    position_id: Optional[int] = None
    token_symbol: str
    token_address: str = Field(..., regex=ValidationConstants.ETHEREUM_ADDRESS_PATTERN)
    amount_shorted: Decimal = Field(..., gt=0)
    entry_price_in_usdc: Decimal = Field(..., gt=ValidationConstants.MIN_PRICE)
    unlock_date: datetime
    status: PositionStatus = PositionStatus.OPEN
    
    # Transaction hashes
    borrow_tx_hash: Optional[str] = Field(None, regex=ValidationConstants.TRANSACTION_HASH_PATTERN)
    swap_tx_hash: Optional[str] = Field(None, regex=ValidationConstants.TRANSACTION_HASH_PATTERN)
    repay_tx_hash: Optional[str] = Field(None, regex=ValidationConstants.TRANSACTION_HASH_PATTERN)
    close_swap_tx_hash: Optional[str] = Field(None, regex=ValidationConstants.TRANSACTION_HASH_PATTERN)
    
    # Position metrics
    current_price: Optional[Decimal] = Field(None, gt=0)
    pnl_usd: Optional[Decimal] = None
    pnl_percentage: Optional[float] = None
    
    # Risk management
    stop_loss_price: Optional[Decimal] = Field(None, gt=0)
    take_profit_price: Optional[Decimal] = Field(None, gt=0)
    
    # Timestamps
    entry_time: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    close_time: Optional[datetime] = None
    close_reason: Optional[str] = None
    
    # Additional metadata
    strategy_id: str = "pre_unlock_decay_v1"
    pressure_score: Optional[float] = Field(None, ge=0, le=10)
    notes: Optional[str] = None
    
    @validator('token_address')
    def validate_address(cls, v):
        return v.lower()
    
    @root_validator
    def validate_prices(cls, values):
        entry_price = values.get('entry_price_in_usdc')
        stop_loss = values.get('stop_loss_price')
        take_profit = values.get('take_profit_price')
        
        if entry_price and stop_loss and take_profit:
            # For short positions: stop_loss > entry_price > take_profit
            if stop_loss <= entry_price:
                raise ValueError('Stop loss price must be higher than entry price for short positions')
            if take_profit >= entry_price:
                raise ValueError('Take profit price must be lower than entry price for short positions')
        
        return values


class RiskAlert(ChimeraBaseModel):
    """Risk management alert model"""
    position_id: int
    token_symbol: str
    alert_type: AlertType
    current_price: Decimal = Field(..., gt=0)
    trigger_price: Decimal = Field(..., gt=0)
    risk_level: str = "MEDIUM"
    action_required: str
    message: str
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    # Additional context
    pnl_percentage: Optional[float] = None
    distance_to_stop_loss: Optional[float] = None
    market_condition: Optional[MarketCondition] = None


class ExecutionSignal(ChimeraBaseModel):
    """Trade execution signal model"""
    token_symbol: str
    contract_address: str = Field(..., regex=ValidationConstants.ETHEREUM_ADDRESS_PATTERN)
    signal_type: ExecutionSignalType
    current_price: Decimal = Field(..., gt=0)
    target_price: Optional[Decimal] = Field(None, gt=0)
    confidence: float = Field(..., ge=0, le=1)
    slippage_risk: SlippageRisk
    market_condition: MarketCondition
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    reason: str
    
    @validator('contract_address')
    def validate_address(cls, v):
        return v.lower()


# =============================================================================
# SYSTEM MODELS
# =============================================================================

class ServiceHealth(ChimeraBaseModel):
    """Service health status model"""
    service_name: str
    status: str  # "healthy", "degraded", "unhealthy"
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    uptime_seconds: float
    last_error: Optional[str] = None
    metrics: Dict[str, Any] = Field(default_factory=dict)


class SystemMetrics(ChimeraBaseModel):
    """System-wide metrics model"""
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    active_positions: int = 0
    total_pnl_usd: Decimal = Decimal('0')
    success_rate: float = 0.0
    avg_execution_time_ms: float = 0.0
    error_count_24h: int = 0
    service_health: List[ServiceHealth] = Field(default_factory=list)


class NotificationMessage(ChimeraBaseModel):
    """Notification message model"""
    title: str = Field(..., max_length=100)
    message: str = Field(..., max_length=4000)  # Telegram limit
    priority: int = Field(default=2, ge=1, le=4)  # 1=low, 4=critical
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    service_name: Optional[str] = None
    tags: List[str] = Field(default_factory=list)
    
    # Telegram-specific fields
    parse_mode: Optional[str] = "Markdown"
    disable_notification: bool = False


# =============================================================================
# CONFIGURATION MODELS
# =============================================================================

class TradingConfig(ChimeraBaseModel):
    """Trading configuration model"""
    paper_trading_mode: bool = True
    stop_loss_pct: Decimal = TradingConstants.DEFAULT_STOP_LOSS_PCT
    take_profit_pct: Decimal = TradingConstants.DEFAULT_TAKE_PROFIT_PCT
    position_size_usd: Decimal = TradingConstants.DEFAULT_POSITION_SIZE_USD
    max_positions: int = 10
    pressure_score_threshold: float = 0.75
    monitoring_interval_seconds: int = 60
    
    @validator('stop_loss_pct', 'take_profit_pct')
    def validate_percentages(cls, v):
        if v <= 0 or v >= 1:
            raise ValueError('Percentage must be between 0 and 1')
        return v


class APIConfig(ChimeraBaseModel):
    """API configuration model"""
    rate_limit_per_second: int = 100
    timeout_seconds: int = 30
    max_retries: int = 3
    circuit_breaker_threshold: int = 5
    
    # API Keys (will be loaded from environment)
    infura_api_key: Optional[str] = None
    coingecko_api_key: Optional[str] = None
    telegram_bot_token: Optional[str] = None


# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

def create_position_from_candidate(candidate: TradeCandidate, 
                                 entry_price: Decimal,
                                 amount: Decimal) -> Position:
    """Create a Position from a TradeCandidate"""
    return Position(
        token_symbol=candidate.token_symbol,
        token_address=candidate.contract_address,
        amount_shorted=amount,
        entry_price_in_usdc=entry_price,
        unlock_date=candidate.unlock_date,
        strategy_id=candidate.strategy_id,
        pressure_score=candidate.pressure_score,
        stop_loss_price=entry_price * (1 + TradingConstants.DEFAULT_STOP_LOSS_PCT),
        take_profit_price=entry_price * (1 - TradingConstants.DEFAULT_TAKE_PROFIT_PCT)
    )


def calculate_position_pnl(position: Position, current_price: Decimal) -> Dict[str, Any]:
    """Calculate position P&L metrics"""
    if not position.entry_price_in_usdc or not current_price:
        return {}
    
    # For short positions: profit when price goes down
    pnl_per_token = position.entry_price_in_usdc - current_price
    total_pnl = pnl_per_token * position.amount_shorted
    pnl_percentage = (pnl_per_token / position.entry_price_in_usdc) * 100
    
    return {
        'pnl_usd': float(total_pnl),
        'pnl_percentage': float(pnl_percentage),
        'current_price': float(current_price),
        'entry_price': float(position.entry_price_in_usdc)
    }


# Export all models
__all__ = [
    # Enums
    'PositionStatus', 'AlertType', 'MarketCondition', 'SlippageRisk', 'ExecutionSignalType',
    
    # Core models
    'TokenInfo', 'UnlockEvent', 'TradeCandidate', 'Position', 'RiskAlert', 'ExecutionSignal',
    
    # System models
    'ServiceHealth', 'SystemMetrics', 'NotificationMessage',
    
    # Configuration models
    'TradingConfig', 'APIConfig',
    
    # Utility functions
    'create_position_from_candidate', 'calculate_position_pnl'
]
